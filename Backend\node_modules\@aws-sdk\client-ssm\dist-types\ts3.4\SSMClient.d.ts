import {
  HostHeaderInputConfig,
  HostHeaderResolvedConfig,
} from "@aws-sdk/middleware-host-header";
import {
  UserAgentInputConfig,
  UserAgentResolvedConfig,
} from "@aws-sdk/middleware-user-agent";
import {
  RegionInputConfig,
  RegionResolvedConfig,
} from "@smithy/config-resolver";
import {
  EndpointInputConfig,
  EndpointResolvedConfig,
} from "@smithy/middleware-endpoint";
import {
  RetryInputConfig,
  RetryResolvedConfig,
} from "@smithy/middleware-retry";
import { HttpHandlerUserInput as __HttpHandlerUserInput } from "@smithy/protocol-http";
import {
  Client as __Client,
  DefaultsMode as __DefaultsMode,
  SmithyConfiguration as __SmithyConfiguration,
  SmithyResolvedConfiguration as __SmithyResolvedConfiguration,
} from "@smithy/smithy-client";
import {
  AwsCredentialIdentityProvider,
  BodyLengthCalculator as __BodyLengthCalculator,
  CheckOptionalClientConfig as __CheckOptionalClientConfig,
  ChecksumConstructor as __ChecksumConstructor,
  Decoder as __Decoder,
  Encoder as __Encoder,
  HashConstructor as __HashConstructor,
  HttpHandlerOptions as __HttpHandlerOptions,
  Logger as __Logger,
  Provider as __Provider,
  Provider,
  StreamCollector as __StreamCollector,
  UrlParser as __UrlParser,
  UserAgent as __UserAgent,
} from "@smithy/types";
import {
  HttpAuthSchemeInputConfig,
  HttpAuthSchemeResolvedConfig,
} from "./auth/httpAuthSchemeProvider";
import {
  AddTagsToResourceCommandInput,
  AddTagsToResourceCommandOutput,
} from "./commands/AddTagsToResourceCommand";
import {
  AssociateOpsItemRelatedItemCommandInput,
  AssociateOpsItemRelatedItemCommandOutput,
} from "./commands/AssociateOpsItemRelatedItemCommand";
import {
  CancelCommandCommandInput,
  CancelCommandCommandOutput,
} from "./commands/CancelCommandCommand";
import {
  CancelMaintenanceWindowExecutionCommandInput,
  CancelMaintenanceWindowExecutionCommandOutput,
} from "./commands/CancelMaintenanceWindowExecutionCommand";
import {
  CreateActivationCommandInput,
  CreateActivationCommandOutput,
} from "./commands/CreateActivationCommand";
import {
  CreateAssociationBatchCommandInput,
  CreateAssociationBatchCommandOutput,
} from "./commands/CreateAssociationBatchCommand";
import {
  CreateAssociationCommandInput,
  CreateAssociationCommandOutput,
} from "./commands/CreateAssociationCommand";
import {
  CreateDocumentCommandInput,
  CreateDocumentCommandOutput,
} from "./commands/CreateDocumentCommand";
import {
  CreateMaintenanceWindowCommandInput,
  CreateMaintenanceWindowCommandOutput,
} from "./commands/CreateMaintenanceWindowCommand";
import {
  CreateOpsItemCommandInput,
  CreateOpsItemCommandOutput,
} from "./commands/CreateOpsItemCommand";
import {
  CreateOpsMetadataCommandInput,
  CreateOpsMetadataCommandOutput,
} from "./commands/CreateOpsMetadataCommand";
import {
  CreatePatchBaselineCommandInput,
  CreatePatchBaselineCommandOutput,
} from "./commands/CreatePatchBaselineCommand";
import {
  CreateResourceDataSyncCommandInput,
  CreateResourceDataSyncCommandOutput,
} from "./commands/CreateResourceDataSyncCommand";
import {
  DeleteActivationCommandInput,
  DeleteActivationCommandOutput,
} from "./commands/DeleteActivationCommand";
import {
  DeleteAssociationCommandInput,
  DeleteAssociationCommandOutput,
} from "./commands/DeleteAssociationCommand";
import {
  DeleteDocumentCommandInput,
  DeleteDocumentCommandOutput,
} from "./commands/DeleteDocumentCommand";
import {
  DeleteInventoryCommandInput,
  DeleteInventoryCommandOutput,
} from "./commands/DeleteInventoryCommand";
import {
  DeleteMaintenanceWindowCommandInput,
  DeleteMaintenanceWindowCommandOutput,
} from "./commands/DeleteMaintenanceWindowCommand";
import {
  DeleteOpsItemCommandInput,
  DeleteOpsItemCommandOutput,
} from "./commands/DeleteOpsItemCommand";
import {
  DeleteOpsMetadataCommandInput,
  DeleteOpsMetadataCommandOutput,
} from "./commands/DeleteOpsMetadataCommand";
import {
  DeleteParameterCommandInput,
  DeleteParameterCommandOutput,
} from "./commands/DeleteParameterCommand";
import {
  DeleteParametersCommandInput,
  DeleteParametersCommandOutput,
} from "./commands/DeleteParametersCommand";
import {
  DeletePatchBaselineCommandInput,
  DeletePatchBaselineCommandOutput,
} from "./commands/DeletePatchBaselineCommand";
import {
  DeleteResourceDataSyncCommandInput,
  DeleteResourceDataSyncCommandOutput,
} from "./commands/DeleteResourceDataSyncCommand";
import {
  DeleteResourcePolicyCommandInput,
  DeleteResourcePolicyCommandOutput,
} from "./commands/DeleteResourcePolicyCommand";
import {
  DeregisterManagedInstanceCommandInput,
  DeregisterManagedInstanceCommandOutput,
} from "./commands/DeregisterManagedInstanceCommand";
import {
  DeregisterPatchBaselineForPatchGroupCommandInput,
  DeregisterPatchBaselineForPatchGroupCommandOutput,
} from "./commands/DeregisterPatchBaselineForPatchGroupCommand";
import {
  DeregisterTargetFromMaintenanceWindowCommandInput,
  DeregisterTargetFromMaintenanceWindowCommandOutput,
} from "./commands/DeregisterTargetFromMaintenanceWindowCommand";
import {
  DeregisterTaskFromMaintenanceWindowCommandInput,
  DeregisterTaskFromMaintenanceWindowCommandOutput,
} from "./commands/DeregisterTaskFromMaintenanceWindowCommand";
import {
  DescribeActivationsCommandInput,
  DescribeActivationsCommandOutput,
} from "./commands/DescribeActivationsCommand";
import {
  DescribeAssociationCommandInput,
  DescribeAssociationCommandOutput,
} from "./commands/DescribeAssociationCommand";
import {
  DescribeAssociationExecutionsCommandInput,
  DescribeAssociationExecutionsCommandOutput,
} from "./commands/DescribeAssociationExecutionsCommand";
import {
  DescribeAssociationExecutionTargetsCommandInput,
  DescribeAssociationExecutionTargetsCommandOutput,
} from "./commands/DescribeAssociationExecutionTargetsCommand";
import {
  DescribeAutomationExecutionsCommandInput,
  DescribeAutomationExecutionsCommandOutput,
} from "./commands/DescribeAutomationExecutionsCommand";
import {
  DescribeAutomationStepExecutionsCommandInput,
  DescribeAutomationStepExecutionsCommandOutput,
} from "./commands/DescribeAutomationStepExecutionsCommand";
import {
  DescribeAvailablePatchesCommandInput,
  DescribeAvailablePatchesCommandOutput,
} from "./commands/DescribeAvailablePatchesCommand";
import {
  DescribeDocumentCommandInput,
  DescribeDocumentCommandOutput,
} from "./commands/DescribeDocumentCommand";
import {
  DescribeDocumentPermissionCommandInput,
  DescribeDocumentPermissionCommandOutput,
} from "./commands/DescribeDocumentPermissionCommand";
import {
  DescribeEffectiveInstanceAssociationsCommandInput,
  DescribeEffectiveInstanceAssociationsCommandOutput,
} from "./commands/DescribeEffectiveInstanceAssociationsCommand";
import {
  DescribeEffectivePatchesForPatchBaselineCommandInput,
  DescribeEffectivePatchesForPatchBaselineCommandOutput,
} from "./commands/DescribeEffectivePatchesForPatchBaselineCommand";
import {
  DescribeInstanceAssociationsStatusCommandInput,
  DescribeInstanceAssociationsStatusCommandOutput,
} from "./commands/DescribeInstanceAssociationsStatusCommand";
import {
  DescribeInstanceInformationCommandInput,
  DescribeInstanceInformationCommandOutput,
} from "./commands/DescribeInstanceInformationCommand";
import {
  DescribeInstancePatchesCommandInput,
  DescribeInstancePatchesCommandOutput,
} from "./commands/DescribeInstancePatchesCommand";
import {
  DescribeInstancePatchStatesCommandInput,
  DescribeInstancePatchStatesCommandOutput,
} from "./commands/DescribeInstancePatchStatesCommand";
import {
  DescribeInstancePatchStatesForPatchGroupCommandInput,
  DescribeInstancePatchStatesForPatchGroupCommandOutput,
} from "./commands/DescribeInstancePatchStatesForPatchGroupCommand";
import {
  DescribeInstancePropertiesCommandInput,
  DescribeInstancePropertiesCommandOutput,
} from "./commands/DescribeInstancePropertiesCommand";
import {
  DescribeInventoryDeletionsCommandInput,
  DescribeInventoryDeletionsCommandOutput,
} from "./commands/DescribeInventoryDeletionsCommand";
import {
  DescribeMaintenanceWindowExecutionsCommandInput,
  DescribeMaintenanceWindowExecutionsCommandOutput,
} from "./commands/DescribeMaintenanceWindowExecutionsCommand";
import {
  DescribeMaintenanceWindowExecutionTaskInvocationsCommandInput,
  DescribeMaintenanceWindowExecutionTaskInvocationsCommandOutput,
} from "./commands/DescribeMaintenanceWindowExecutionTaskInvocationsCommand";
import {
  DescribeMaintenanceWindowExecutionTasksCommandInput,
  DescribeMaintenanceWindowExecutionTasksCommandOutput,
} from "./commands/DescribeMaintenanceWindowExecutionTasksCommand";
import {
  DescribeMaintenanceWindowScheduleCommandInput,
  DescribeMaintenanceWindowScheduleCommandOutput,
} from "./commands/DescribeMaintenanceWindowScheduleCommand";
import {
  DescribeMaintenanceWindowsCommandInput,
  DescribeMaintenanceWindowsCommandOutput,
} from "./commands/DescribeMaintenanceWindowsCommand";
import {
  DescribeMaintenanceWindowsForTargetCommandInput,
  DescribeMaintenanceWindowsForTargetCommandOutput,
} from "./commands/DescribeMaintenanceWindowsForTargetCommand";
import {
  DescribeMaintenanceWindowTargetsCommandInput,
  DescribeMaintenanceWindowTargetsCommandOutput,
} from "./commands/DescribeMaintenanceWindowTargetsCommand";
import {
  DescribeMaintenanceWindowTasksCommandInput,
  DescribeMaintenanceWindowTasksCommandOutput,
} from "./commands/DescribeMaintenanceWindowTasksCommand";
import {
  DescribeOpsItemsCommandInput,
  DescribeOpsItemsCommandOutput,
} from "./commands/DescribeOpsItemsCommand";
import {
  DescribeParametersCommandInput,
  DescribeParametersCommandOutput,
} from "./commands/DescribeParametersCommand";
import {
  DescribePatchBaselinesCommandInput,
  DescribePatchBaselinesCommandOutput,
} from "./commands/DescribePatchBaselinesCommand";
import {
  DescribePatchGroupsCommandInput,
  DescribePatchGroupsCommandOutput,
} from "./commands/DescribePatchGroupsCommand";
import {
  DescribePatchGroupStateCommandInput,
  DescribePatchGroupStateCommandOutput,
} from "./commands/DescribePatchGroupStateCommand";
import {
  DescribePatchPropertiesCommandInput,
  DescribePatchPropertiesCommandOutput,
} from "./commands/DescribePatchPropertiesCommand";
import {
  DescribeSessionsCommandInput,
  DescribeSessionsCommandOutput,
} from "./commands/DescribeSessionsCommand";
import {
  DisassociateOpsItemRelatedItemCommandInput,
  DisassociateOpsItemRelatedItemCommandOutput,
} from "./commands/DisassociateOpsItemRelatedItemCommand";
import {
  GetAccessTokenCommandInput,
  GetAccessTokenCommandOutput,
} from "./commands/GetAccessTokenCommand";
import {
  GetAutomationExecutionCommandInput,
  GetAutomationExecutionCommandOutput,
} from "./commands/GetAutomationExecutionCommand";
import {
  GetCalendarStateCommandInput,
  GetCalendarStateCommandOutput,
} from "./commands/GetCalendarStateCommand";
import {
  GetCommandInvocationCommandInput,
  GetCommandInvocationCommandOutput,
} from "./commands/GetCommandInvocationCommand";
import {
  GetConnectionStatusCommandInput,
  GetConnectionStatusCommandOutput,
} from "./commands/GetConnectionStatusCommand";
import {
  GetDefaultPatchBaselineCommandInput,
  GetDefaultPatchBaselineCommandOutput,
} from "./commands/GetDefaultPatchBaselineCommand";
import {
  GetDeployablePatchSnapshotForInstanceCommandInput,
  GetDeployablePatchSnapshotForInstanceCommandOutput,
} from "./commands/GetDeployablePatchSnapshotForInstanceCommand";
import {
  GetDocumentCommandInput,
  GetDocumentCommandOutput,
} from "./commands/GetDocumentCommand";
import {
  GetExecutionPreviewCommandInput,
  GetExecutionPreviewCommandOutput,
} from "./commands/GetExecutionPreviewCommand";
import {
  GetInventoryCommandInput,
  GetInventoryCommandOutput,
} from "./commands/GetInventoryCommand";
import {
  GetInventorySchemaCommandInput,
  GetInventorySchemaCommandOutput,
} from "./commands/GetInventorySchemaCommand";
import {
  GetMaintenanceWindowCommandInput,
  GetMaintenanceWindowCommandOutput,
} from "./commands/GetMaintenanceWindowCommand";
import {
  GetMaintenanceWindowExecutionCommandInput,
  GetMaintenanceWindowExecutionCommandOutput,
} from "./commands/GetMaintenanceWindowExecutionCommand";
import {
  GetMaintenanceWindowExecutionTaskCommandInput,
  GetMaintenanceWindowExecutionTaskCommandOutput,
} from "./commands/GetMaintenanceWindowExecutionTaskCommand";
import {
  GetMaintenanceWindowExecutionTaskInvocationCommandInput,
  GetMaintenanceWindowExecutionTaskInvocationCommandOutput,
} from "./commands/GetMaintenanceWindowExecutionTaskInvocationCommand";
import {
  GetMaintenanceWindowTaskCommandInput,
  GetMaintenanceWindowTaskCommandOutput,
} from "./commands/GetMaintenanceWindowTaskCommand";
import {
  GetOpsItemCommandInput,
  GetOpsItemCommandOutput,
} from "./commands/GetOpsItemCommand";
import {
  GetOpsMetadataCommandInput,
  GetOpsMetadataCommandOutput,
} from "./commands/GetOpsMetadataCommand";
import {
  GetOpsSummaryCommandInput,
  GetOpsSummaryCommandOutput,
} from "./commands/GetOpsSummaryCommand";
import {
  GetParameterCommandInput,
  GetParameterCommandOutput,
} from "./commands/GetParameterCommand";
import {
  GetParameterHistoryCommandInput,
  GetParameterHistoryCommandOutput,
} from "./commands/GetParameterHistoryCommand";
import {
  GetParametersByPathCommandInput,
  GetParametersByPathCommandOutput,
} from "./commands/GetParametersByPathCommand";
import {
  GetParametersCommandInput,
  GetParametersCommandOutput,
} from "./commands/GetParametersCommand";
import {
  GetPatchBaselineCommandInput,
  GetPatchBaselineCommandOutput,
} from "./commands/GetPatchBaselineCommand";
import {
  GetPatchBaselineForPatchGroupCommandInput,
  GetPatchBaselineForPatchGroupCommandOutput,
} from "./commands/GetPatchBaselineForPatchGroupCommand";
import {
  GetResourcePoliciesCommandInput,
  GetResourcePoliciesCommandOutput,
} from "./commands/GetResourcePoliciesCommand";
import {
  GetServiceSettingCommandInput,
  GetServiceSettingCommandOutput,
} from "./commands/GetServiceSettingCommand";
import {
  LabelParameterVersionCommandInput,
  LabelParameterVersionCommandOutput,
} from "./commands/LabelParameterVersionCommand";
import {
  ListAssociationsCommandInput,
  ListAssociationsCommandOutput,
} from "./commands/ListAssociationsCommand";
import {
  ListAssociationVersionsCommandInput,
  ListAssociationVersionsCommandOutput,
} from "./commands/ListAssociationVersionsCommand";
import {
  ListCommandInvocationsCommandInput,
  ListCommandInvocationsCommandOutput,
} from "./commands/ListCommandInvocationsCommand";
import {
  ListCommandsCommandInput,
  ListCommandsCommandOutput,
} from "./commands/ListCommandsCommand";
import {
  ListComplianceItemsCommandInput,
  ListComplianceItemsCommandOutput,
} from "./commands/ListComplianceItemsCommand";
import {
  ListComplianceSummariesCommandInput,
  ListComplianceSummariesCommandOutput,
} from "./commands/ListComplianceSummariesCommand";
import {
  ListDocumentMetadataHistoryCommandInput,
  ListDocumentMetadataHistoryCommandOutput,
} from "./commands/ListDocumentMetadataHistoryCommand";
import {
  ListDocumentsCommandInput,
  ListDocumentsCommandOutput,
} from "./commands/ListDocumentsCommand";
import {
  ListDocumentVersionsCommandInput,
  ListDocumentVersionsCommandOutput,
} from "./commands/ListDocumentVersionsCommand";
import {
  ListInventoryEntriesCommandInput,
  ListInventoryEntriesCommandOutput,
} from "./commands/ListInventoryEntriesCommand";
import {
  ListNodesCommandInput,
  ListNodesCommandOutput,
} from "./commands/ListNodesCommand";
import {
  ListNodesSummaryCommandInput,
  ListNodesSummaryCommandOutput,
} from "./commands/ListNodesSummaryCommand";
import {
  ListOpsItemEventsCommandInput,
  ListOpsItemEventsCommandOutput,
} from "./commands/ListOpsItemEventsCommand";
import {
  ListOpsItemRelatedItemsCommandInput,
  ListOpsItemRelatedItemsCommandOutput,
} from "./commands/ListOpsItemRelatedItemsCommand";
import {
  ListOpsMetadataCommandInput,
  ListOpsMetadataCommandOutput,
} from "./commands/ListOpsMetadataCommand";
import {
  ListResourceComplianceSummariesCommandInput,
  ListResourceComplianceSummariesCommandOutput,
} from "./commands/ListResourceComplianceSummariesCommand";
import {
  ListResourceDataSyncCommandInput,
  ListResourceDataSyncCommandOutput,
} from "./commands/ListResourceDataSyncCommand";
import {
  ListTagsForResourceCommandInput,
  ListTagsForResourceCommandOutput,
} from "./commands/ListTagsForResourceCommand";
import {
  ModifyDocumentPermissionCommandInput,
  ModifyDocumentPermissionCommandOutput,
} from "./commands/ModifyDocumentPermissionCommand";
import {
  PutComplianceItemsCommandInput,
  PutComplianceItemsCommandOutput,
} from "./commands/PutComplianceItemsCommand";
import {
  PutInventoryCommandInput,
  PutInventoryCommandOutput,
} from "./commands/PutInventoryCommand";
import {
  PutParameterCommandInput,
  PutParameterCommandOutput,
} from "./commands/PutParameterCommand";
import {
  PutResourcePolicyCommandInput,
  PutResourcePolicyCommandOutput,
} from "./commands/PutResourcePolicyCommand";
import {
  RegisterDefaultPatchBaselineCommandInput,
  RegisterDefaultPatchBaselineCommandOutput,
} from "./commands/RegisterDefaultPatchBaselineCommand";
import {
  RegisterPatchBaselineForPatchGroupCommandInput,
  RegisterPatchBaselineForPatchGroupCommandOutput,
} from "./commands/RegisterPatchBaselineForPatchGroupCommand";
import {
  RegisterTargetWithMaintenanceWindowCommandInput,
  RegisterTargetWithMaintenanceWindowCommandOutput,
} from "./commands/RegisterTargetWithMaintenanceWindowCommand";
import {
  RegisterTaskWithMaintenanceWindowCommandInput,
  RegisterTaskWithMaintenanceWindowCommandOutput,
} from "./commands/RegisterTaskWithMaintenanceWindowCommand";
import {
  RemoveTagsFromResourceCommandInput,
  RemoveTagsFromResourceCommandOutput,
} from "./commands/RemoveTagsFromResourceCommand";
import {
  ResetServiceSettingCommandInput,
  ResetServiceSettingCommandOutput,
} from "./commands/ResetServiceSettingCommand";
import {
  ResumeSessionCommandInput,
  ResumeSessionCommandOutput,
} from "./commands/ResumeSessionCommand";
import {
  SendAutomationSignalCommandInput,
  SendAutomationSignalCommandOutput,
} from "./commands/SendAutomationSignalCommand";
import {
  SendCommandCommandInput,
  SendCommandCommandOutput,
} from "./commands/SendCommandCommand";
import {
  StartAccessRequestCommandInput,
  StartAccessRequestCommandOutput,
} from "./commands/StartAccessRequestCommand";
import {
  StartAssociationsOnceCommandInput,
  StartAssociationsOnceCommandOutput,
} from "./commands/StartAssociationsOnceCommand";
import {
  StartAutomationExecutionCommandInput,
  StartAutomationExecutionCommandOutput,
} from "./commands/StartAutomationExecutionCommand";
import {
  StartChangeRequestExecutionCommandInput,
  StartChangeRequestExecutionCommandOutput,
} from "./commands/StartChangeRequestExecutionCommand";
import {
  StartExecutionPreviewCommandInput,
  StartExecutionPreviewCommandOutput,
} from "./commands/StartExecutionPreviewCommand";
import {
  StartSessionCommandInput,
  StartSessionCommandOutput,
} from "./commands/StartSessionCommand";
import {
  StopAutomationExecutionCommandInput,
  StopAutomationExecutionCommandOutput,
} from "./commands/StopAutomationExecutionCommand";
import {
  TerminateSessionCommandInput,
  TerminateSessionCommandOutput,
} from "./commands/TerminateSessionCommand";
import {
  UnlabelParameterVersionCommandInput,
  UnlabelParameterVersionCommandOutput,
} from "./commands/UnlabelParameterVersionCommand";
import {
  UpdateAssociationCommandInput,
  UpdateAssociationCommandOutput,
} from "./commands/UpdateAssociationCommand";
import {
  UpdateAssociationStatusCommandInput,
  UpdateAssociationStatusCommandOutput,
} from "./commands/UpdateAssociationStatusCommand";
import {
  UpdateDocumentCommandInput,
  UpdateDocumentCommandOutput,
} from "./commands/UpdateDocumentCommand";
import {
  UpdateDocumentDefaultVersionCommandInput,
  UpdateDocumentDefaultVersionCommandOutput,
} from "./commands/UpdateDocumentDefaultVersionCommand";
import {
  UpdateDocumentMetadataCommandInput,
  UpdateDocumentMetadataCommandOutput,
} from "./commands/UpdateDocumentMetadataCommand";
import {
  UpdateMaintenanceWindowCommandInput,
  UpdateMaintenanceWindowCommandOutput,
} from "./commands/UpdateMaintenanceWindowCommand";
import {
  UpdateMaintenanceWindowTargetCommandInput,
  UpdateMaintenanceWindowTargetCommandOutput,
} from "./commands/UpdateMaintenanceWindowTargetCommand";
import {
  UpdateMaintenanceWindowTaskCommandInput,
  UpdateMaintenanceWindowTaskCommandOutput,
} from "./commands/UpdateMaintenanceWindowTaskCommand";
import {
  UpdateManagedInstanceRoleCommandInput,
  UpdateManagedInstanceRoleCommandOutput,
} from "./commands/UpdateManagedInstanceRoleCommand";
import {
  UpdateOpsItemCommandInput,
  UpdateOpsItemCommandOutput,
} from "./commands/UpdateOpsItemCommand";
import {
  UpdateOpsMetadataCommandInput,
  UpdateOpsMetadataCommandOutput,
} from "./commands/UpdateOpsMetadataCommand";
import {
  UpdatePatchBaselineCommandInput,
  UpdatePatchBaselineCommandOutput,
} from "./commands/UpdatePatchBaselineCommand";
import {
  UpdateResourceDataSyncCommandInput,
  UpdateResourceDataSyncCommandOutput,
} from "./commands/UpdateResourceDataSyncCommand";
import {
  UpdateServiceSettingCommandInput,
  UpdateServiceSettingCommandOutput,
} from "./commands/UpdateServiceSettingCommand";
import {
  ClientInputEndpointParameters,
  ClientResolvedEndpointParameters,
  EndpointParameters,
} from "./endpoint/EndpointParameters";
import { RuntimeExtension, RuntimeExtensionsConfig } from "./runtimeExtensions";
export { __Client };
export type ServiceInputTypes =
  | AddTagsToResourceCommandInput
  | AssociateOpsItemRelatedItemCommandInput
  | CancelCommandCommandInput
  | CancelMaintenanceWindowExecutionCommandInput
  | CreateActivationCommandInput
  | CreateAssociationBatchCommandInput
  | CreateAssociationCommandInput
  | CreateDocumentCommandInput
  | CreateMaintenanceWindowCommandInput
  | CreateOpsItemCommandInput
  | CreateOpsMetadataCommandInput
  | CreatePatchBaselineCommandInput
  | CreateResourceDataSyncCommandInput
  | DeleteActivationCommandInput
  | DeleteAssociationCommandInput
  | DeleteDocumentCommandInput
  | DeleteInventoryCommandInput
  | DeleteMaintenanceWindowCommandInput
  | DeleteOpsItemCommandInput
  | DeleteOpsMetadataCommandInput
  | DeleteParameterCommandInput
  | DeleteParametersCommandInput
  | DeletePatchBaselineCommandInput
  | DeleteResourceDataSyncCommandInput
  | DeleteResourcePolicyCommandInput
  | DeregisterManagedInstanceCommandInput
  | DeregisterPatchBaselineForPatchGroupCommandInput
  | DeregisterTargetFromMaintenanceWindowCommandInput
  | DeregisterTaskFromMaintenanceWindowCommandInput
  | DescribeActivationsCommandInput
  | DescribeAssociationCommandInput
  | DescribeAssociationExecutionTargetsCommandInput
  | DescribeAssociationExecutionsCommandInput
  | DescribeAutomationExecutionsCommandInput
  | DescribeAutomationStepExecutionsCommandInput
  | DescribeAvailablePatchesCommandInput
  | DescribeDocumentCommandInput
  | DescribeDocumentPermissionCommandInput
  | DescribeEffectiveInstanceAssociationsCommandInput
  | DescribeEffectivePatchesForPatchBaselineCommandInput
  | DescribeInstanceAssociationsStatusCommandInput
  | DescribeInstanceInformationCommandInput
  | DescribeInstancePatchStatesCommandInput
  | DescribeInstancePatchStatesForPatchGroupCommandInput
  | DescribeInstancePatchesCommandInput
  | DescribeInstancePropertiesCommandInput
  | DescribeInventoryDeletionsCommandInput
  | DescribeMaintenanceWindowExecutionTaskInvocationsCommandInput
  | DescribeMaintenanceWindowExecutionTasksCommandInput
  | DescribeMaintenanceWindowExecutionsCommandInput
  | DescribeMaintenanceWindowScheduleCommandInput
  | DescribeMaintenanceWindowTargetsCommandInput
  | DescribeMaintenanceWindowTasksCommandInput
  | DescribeMaintenanceWindowsCommandInput
  | DescribeMaintenanceWindowsForTargetCommandInput
  | DescribeOpsItemsCommandInput
  | DescribeParametersCommandInput
  | DescribePatchBaselinesCommandInput
  | DescribePatchGroupStateCommandInput
  | DescribePatchGroupsCommandInput
  | DescribePatchPropertiesCommandInput
  | DescribeSessionsCommandInput
  | DisassociateOpsItemRelatedItemCommandInput
  | GetAccessTokenCommandInput
  | GetAutomationExecutionCommandInput
  | GetCalendarStateCommandInput
  | GetCommandInvocationCommandInput
  | GetConnectionStatusCommandInput
  | GetDefaultPatchBaselineCommandInput
  | GetDeployablePatchSnapshotForInstanceCommandInput
  | GetDocumentCommandInput
  | GetExecutionPreviewCommandInput
  | GetInventoryCommandInput
  | GetInventorySchemaCommandInput
  | GetMaintenanceWindowCommandInput
  | GetMaintenanceWindowExecutionCommandInput
  | GetMaintenanceWindowExecutionTaskCommandInput
  | GetMaintenanceWindowExecutionTaskInvocationCommandInput
  | GetMaintenanceWindowTaskCommandInput
  | GetOpsItemCommandInput
  | GetOpsMetadataCommandInput
  | GetOpsSummaryCommandInput
  | GetParameterCommandInput
  | GetParameterHistoryCommandInput
  | GetParametersByPathCommandInput
  | GetParametersCommandInput
  | GetPatchBaselineCommandInput
  | GetPatchBaselineForPatchGroupCommandInput
  | GetResourcePoliciesCommandInput
  | GetServiceSettingCommandInput
  | LabelParameterVersionCommandInput
  | ListAssociationVersionsCommandInput
  | ListAssociationsCommandInput
  | ListCommandInvocationsCommandInput
  | ListCommandsCommandInput
  | ListComplianceItemsCommandInput
  | ListComplianceSummariesCommandInput
  | ListDocumentMetadataHistoryCommandInput
  | ListDocumentVersionsCommandInput
  | ListDocumentsCommandInput
  | ListInventoryEntriesCommandInput
  | ListNodesCommandInput
  | ListNodesSummaryCommandInput
  | ListOpsItemEventsCommandInput
  | ListOpsItemRelatedItemsCommandInput
  | ListOpsMetadataCommandInput
  | ListResourceComplianceSummariesCommandInput
  | ListResourceDataSyncCommandInput
  | ListTagsForResourceCommandInput
  | ModifyDocumentPermissionCommandInput
  | PutComplianceItemsCommandInput
  | PutInventoryCommandInput
  | PutParameterCommandInput
  | PutResourcePolicyCommandInput
  | RegisterDefaultPatchBaselineCommandInput
  | RegisterPatchBaselineForPatchGroupCommandInput
  | RegisterTargetWithMaintenanceWindowCommandInput
  | RegisterTaskWithMaintenanceWindowCommandInput
  | RemoveTagsFromResourceCommandInput
  | ResetServiceSettingCommandInput
  | ResumeSessionCommandInput
  | SendAutomationSignalCommandInput
  | SendCommandCommandInput
  | StartAccessRequestCommandInput
  | StartAssociationsOnceCommandInput
  | StartAutomationExecutionCommandInput
  | StartChangeRequestExecutionCommandInput
  | StartExecutionPreviewCommandInput
  | StartSessionCommandInput
  | StopAutomationExecutionCommandInput
  | TerminateSessionCommandInput
  | UnlabelParameterVersionCommandInput
  | UpdateAssociationCommandInput
  | UpdateAssociationStatusCommandInput
  | UpdateDocumentCommandInput
  | UpdateDocumentDefaultVersionCommandInput
  | UpdateDocumentMetadataCommandInput
  | UpdateMaintenanceWindowCommandInput
  | UpdateMaintenanceWindowTargetCommandInput
  | UpdateMaintenanceWindowTaskCommandInput
  | UpdateManagedInstanceRoleCommandInput
  | UpdateOpsItemCommandInput
  | UpdateOpsMetadataCommandInput
  | UpdatePatchBaselineCommandInput
  | UpdateResourceDataSyncCommandInput
  | UpdateServiceSettingCommandInput;
export type ServiceOutputTypes =
  | AddTagsToResourceCommandOutput
  | AssociateOpsItemRelatedItemCommandOutput
  | CancelCommandCommandOutput
  | CancelMaintenanceWindowExecutionCommandOutput
  | CreateActivationCommandOutput
  | CreateAssociationBatchCommandOutput
  | CreateAssociationCommandOutput
  | CreateDocumentCommandOutput
  | CreateMaintenanceWindowCommandOutput
  | CreateOpsItemCommandOutput
  | CreateOpsMetadataCommandOutput
  | CreatePatchBaselineCommandOutput
  | CreateResourceDataSyncCommandOutput
  | DeleteActivationCommandOutput
  | DeleteAssociationCommandOutput
  | DeleteDocumentCommandOutput
  | DeleteInventoryCommandOutput
  | DeleteMaintenanceWindowCommandOutput
  | DeleteOpsItemCommandOutput
  | DeleteOpsMetadataCommandOutput
  | DeleteParameterCommandOutput
  | DeleteParametersCommandOutput
  | DeletePatchBaselineCommandOutput
  | DeleteResourceDataSyncCommandOutput
  | DeleteResourcePolicyCommandOutput
  | DeregisterManagedInstanceCommandOutput
  | DeregisterPatchBaselineForPatchGroupCommandOutput
  | DeregisterTargetFromMaintenanceWindowCommandOutput
  | DeregisterTaskFromMaintenanceWindowCommandOutput
  | DescribeActivationsCommandOutput
  | DescribeAssociationCommandOutput
  | DescribeAssociationExecutionTargetsCommandOutput
  | DescribeAssociationExecutionsCommandOutput
  | DescribeAutomationExecutionsCommandOutput
  | DescribeAutomationStepExecutionsCommandOutput
  | DescribeAvailablePatchesCommandOutput
  | DescribeDocumentCommandOutput
  | DescribeDocumentPermissionCommandOutput
  | DescribeEffectiveInstanceAssociationsCommandOutput
  | DescribeEffectivePatchesForPatchBaselineCommandOutput
  | DescribeInstanceAssociationsStatusCommandOutput
  | DescribeInstanceInformationCommandOutput
  | DescribeInstancePatchStatesCommandOutput
  | DescribeInstancePatchStatesForPatchGroupCommandOutput
  | DescribeInstancePatchesCommandOutput
  | DescribeInstancePropertiesCommandOutput
  | DescribeInventoryDeletionsCommandOutput
  | DescribeMaintenanceWindowExecutionTaskInvocationsCommandOutput
  | DescribeMaintenanceWindowExecutionTasksCommandOutput
  | DescribeMaintenanceWindowExecutionsCommandOutput
  | DescribeMaintenanceWindowScheduleCommandOutput
  | DescribeMaintenanceWindowTargetsCommandOutput
  | DescribeMaintenanceWindowTasksCommandOutput
  | DescribeMaintenanceWindowsCommandOutput
  | DescribeMaintenanceWindowsForTargetCommandOutput
  | DescribeOpsItemsCommandOutput
  | DescribeParametersCommandOutput
  | DescribePatchBaselinesCommandOutput
  | DescribePatchGroupStateCommandOutput
  | DescribePatchGroupsCommandOutput
  | DescribePatchPropertiesCommandOutput
  | DescribeSessionsCommandOutput
  | DisassociateOpsItemRelatedItemCommandOutput
  | GetAccessTokenCommandOutput
  | GetAutomationExecutionCommandOutput
  | GetCalendarStateCommandOutput
  | GetCommandInvocationCommandOutput
  | GetConnectionStatusCommandOutput
  | GetDefaultPatchBaselineCommandOutput
  | GetDeployablePatchSnapshotForInstanceCommandOutput
  | GetDocumentCommandOutput
  | GetExecutionPreviewCommandOutput
  | GetInventoryCommandOutput
  | GetInventorySchemaCommandOutput
  | GetMaintenanceWindowCommandOutput
  | GetMaintenanceWindowExecutionCommandOutput
  | GetMaintenanceWindowExecutionTaskCommandOutput
  | GetMaintenanceWindowExecutionTaskInvocationCommandOutput
  | GetMaintenanceWindowTaskCommandOutput
  | GetOpsItemCommandOutput
  | GetOpsMetadataCommandOutput
  | GetOpsSummaryCommandOutput
  | GetParameterCommandOutput
  | GetParameterHistoryCommandOutput
  | GetParametersByPathCommandOutput
  | GetParametersCommandOutput
  | GetPatchBaselineCommandOutput
  | GetPatchBaselineForPatchGroupCommandOutput
  | GetResourcePoliciesCommandOutput
  | GetServiceSettingCommandOutput
  | LabelParameterVersionCommandOutput
  | ListAssociationVersionsCommandOutput
  | ListAssociationsCommandOutput
  | ListCommandInvocationsCommandOutput
  | ListCommandsCommandOutput
  | ListComplianceItemsCommandOutput
  | ListComplianceSummariesCommandOutput
  | ListDocumentMetadataHistoryCommandOutput
  | ListDocumentVersionsCommandOutput
  | ListDocumentsCommandOutput
  | ListInventoryEntriesCommandOutput
  | ListNodesCommandOutput
  | ListNodesSummaryCommandOutput
  | ListOpsItemEventsCommandOutput
  | ListOpsItemRelatedItemsCommandOutput
  | ListOpsMetadataCommandOutput
  | ListResourceComplianceSummariesCommandOutput
  | ListResourceDataSyncCommandOutput
  | ListTagsForResourceCommandOutput
  | ModifyDocumentPermissionCommandOutput
  | PutComplianceItemsCommandOutput
  | PutInventoryCommandOutput
  | PutParameterCommandOutput
  | PutResourcePolicyCommandOutput
  | RegisterDefaultPatchBaselineCommandOutput
  | RegisterPatchBaselineForPatchGroupCommandOutput
  | RegisterTargetWithMaintenanceWindowCommandOutput
  | RegisterTaskWithMaintenanceWindowCommandOutput
  | RemoveTagsFromResourceCommandOutput
  | ResetServiceSettingCommandOutput
  | ResumeSessionCommandOutput
  | SendAutomationSignalCommandOutput
  | SendCommandCommandOutput
  | StartAccessRequestCommandOutput
  | StartAssociationsOnceCommandOutput
  | StartAutomationExecutionCommandOutput
  | StartChangeRequestExecutionCommandOutput
  | StartExecutionPreviewCommandOutput
  | StartSessionCommandOutput
  | StopAutomationExecutionCommandOutput
  | TerminateSessionCommandOutput
  | UnlabelParameterVersionCommandOutput
  | UpdateAssociationCommandOutput
  | UpdateAssociationStatusCommandOutput
  | UpdateDocumentCommandOutput
  | UpdateDocumentDefaultVersionCommandOutput
  | UpdateDocumentMetadataCommandOutput
  | UpdateMaintenanceWindowCommandOutput
  | UpdateMaintenanceWindowTargetCommandOutput
  | UpdateMaintenanceWindowTaskCommandOutput
  | UpdateManagedInstanceRoleCommandOutput
  | UpdateOpsItemCommandOutput
  | UpdateOpsMetadataCommandOutput
  | UpdatePatchBaselineCommandOutput
  | UpdateResourceDataSyncCommandOutput
  | UpdateServiceSettingCommandOutput;
export interface ClientDefaults
  extends Partial<__SmithyConfiguration<__HttpHandlerOptions>> {
  requestHandler?: __HttpHandlerUserInput;
  sha256?: __ChecksumConstructor | __HashConstructor;
  urlParser?: __UrlParser;
  bodyLengthChecker?: __BodyLengthCalculator;
  streamCollector?: __StreamCollector;
  base64Decoder?: __Decoder;
  base64Encoder?: __Encoder;
  utf8Decoder?: __Decoder;
  utf8Encoder?: __Encoder;
  runtime?: string;
  disableHostPrefix?: boolean;
  serviceId?: string;
  useDualstackEndpoint?: boolean | __Provider<boolean>;
  useFipsEndpoint?: boolean | __Provider<boolean>;
  region?: string | __Provider<string>;
  profile?: string;
  defaultUserAgentProvider?: Provider<__UserAgent>;
  credentialDefaultProvider?: (input: any) => AwsCredentialIdentityProvider;
  maxAttempts?: number | __Provider<number>;
  retryMode?: string | __Provider<string>;
  logger?: __Logger;
  extensions?: RuntimeExtension[];
  defaultsMode?: __DefaultsMode | __Provider<__DefaultsMode>;
}
export type SSMClientConfigType = Partial<
  __SmithyConfiguration<__HttpHandlerOptions>
> &
  ClientDefaults &
  UserAgentInputConfig &
  RetryInputConfig &
  RegionInputConfig &
  HostHeaderInputConfig &
  EndpointInputConfig<EndpointParameters> &
  HttpAuthSchemeInputConfig &
  ClientInputEndpointParameters;
export interface SSMClientConfig extends SSMClientConfigType {}
export type SSMClientResolvedConfigType =
  __SmithyResolvedConfiguration<__HttpHandlerOptions> &
    Required<ClientDefaults> &
    RuntimeExtensionsConfig &
    UserAgentResolvedConfig &
    RetryResolvedConfig &
    RegionResolvedConfig &
    HostHeaderResolvedConfig &
    EndpointResolvedConfig<EndpointParameters> &
    HttpAuthSchemeResolvedConfig &
    ClientResolvedEndpointParameters;
export interface SSMClientResolvedConfig extends SSMClientResolvedConfigType {}
export declare class SSMClient extends __Client<
  __HttpHandlerOptions,
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig
> {
  readonly config: SSMClientResolvedConfig;
  constructor(...[configuration]: __CheckOptionalClientConfig<SSMClientConfig>);
  destroy(): void;
}
