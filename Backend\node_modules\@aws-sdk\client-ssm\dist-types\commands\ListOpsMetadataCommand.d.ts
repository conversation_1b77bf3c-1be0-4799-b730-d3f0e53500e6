import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { ListOpsMetadataRequest, ListOpsMetadataResult } from "../models/models_1";
import { ServiceInputTypes, ServiceOutputTypes, SSMClientResolvedConfig } from "../SSMClient";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link ListOpsMetadataCommand}.
 */
export interface ListOpsMetadataCommandInput extends ListOpsMetadataRequest {
}
/**
 * @public
 *
 * The output of {@link ListOpsMetadataCommand}.
 */
export interface ListOpsMetadataCommandOutput extends ListOpsMetadataResult, __MetadataBearer {
}
declare const ListOpsMetadataCommand_base: {
    new (input: ListOpsMetadataCommandInput): import("@smithy/smithy-client").CommandImpl<ListOpsMetadataCommandInput, ListOpsMetadataCommandOutput, SSMClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (...[input]: [] | [ListOpsMetadataCommandInput]): import("@smithy/smithy-client").CommandImpl<ListOpsMetadataCommandInput, ListOpsMetadataCommandOutput, SSMClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Amazon Web Services Systems Manager calls this API operation when displaying all Application Manager OpsMetadata objects or
 *    blobs.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SSMClient, ListOpsMetadataCommand } from "@aws-sdk/client-ssm"; // ES Modules import
 * // const { SSMClient, ListOpsMetadataCommand } = require("@aws-sdk/client-ssm"); // CommonJS import
 * const client = new SSMClient(config);
 * const input = { // ListOpsMetadataRequest
 *   Filters: [ // OpsMetadataFilterList
 *     { // OpsMetadataFilter
 *       Key: "STRING_VALUE", // required
 *       Values: [ // OpsMetadataFilterValueList // required
 *         "STRING_VALUE",
 *       ],
 *     },
 *   ],
 *   MaxResults: Number("int"),
 *   NextToken: "STRING_VALUE",
 * };
 * const command = new ListOpsMetadataCommand(input);
 * const response = await client.send(command);
 * // { // ListOpsMetadataResult
 * //   OpsMetadataList: [ // OpsMetadataList
 * //     { // OpsMetadata
 * //       ResourceId: "STRING_VALUE",
 * //       OpsMetadataArn: "STRING_VALUE",
 * //       LastModifiedDate: new Date("TIMESTAMP"),
 * //       LastModifiedUser: "STRING_VALUE",
 * //       CreationDate: new Date("TIMESTAMP"),
 * //     },
 * //   ],
 * //   NextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param ListOpsMetadataCommandInput - {@link ListOpsMetadataCommandInput}
 * @returns {@link ListOpsMetadataCommandOutput}
 * @see {@link ListOpsMetadataCommandInput} for command's `input` shape.
 * @see {@link ListOpsMetadataCommandOutput} for command's `response` shape.
 * @see {@link SSMClientResolvedConfig | config} for SSMClient's `config` shape.
 *
 * @throws {@link InternalServerError} (server fault)
 *  <p>An error occurred on the server side.</p>
 *
 * @throws {@link OpsMetadataInvalidArgumentException} (client fault)
 *  <p>One of the arguments passed is invalid. </p>
 *
 * @throws {@link SSMServiceException}
 * <p>Base exception class for all service exceptions from SSM service.</p>
 *
 *
 * @public
 */
export declare class ListOpsMetadataCommand extends ListOpsMetadataCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: ListOpsMetadataRequest;
            output: ListOpsMetadataResult;
        };
        sdk: {
            input: ListOpsMetadataCommandInput;
            output: ListOpsMetadataCommandOutput;
        };
    };
}
