import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListAssociationVersionsRequest,
  ListAssociationVersionsResult,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface ListAssociationVersionsCommandInput
  extends ListAssociationVersionsRequest {}
export interface ListAssociationVersionsCommandOutput
  extends ListAssociationVersionsResult,
    __MetadataBearer {}
declare const ListAssociationVersionsCommand_base: {
  new (
    input: ListAssociationVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListAssociationVersionsCommandInput,
    ListAssociationVersionsCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListAssociationVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListAssociationVersionsCommandInput,
    ListAssociationVersionsCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListAssociationVersionsCommand extends ListAssociationVersionsCommand_base {
  protected static __types: {
    api: {
      input: ListAssociationVersionsRequest;
      output: ListAssociationVersionsResult;
    };
    sdk: {
      input: ListAssociationVersionsCommandInput;
      output: ListAssociationVersionsCommandOutput;
    };
  };
}
