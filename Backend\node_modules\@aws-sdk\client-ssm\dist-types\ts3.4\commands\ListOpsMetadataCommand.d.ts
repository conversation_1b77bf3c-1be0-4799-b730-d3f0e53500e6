import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  ListOpsMetadataRequest,
  ListOpsMetadataResult,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface ListOpsMetadataCommandInput extends ListOpsMetadataRequest {}
export interface ListOpsMetadataCommandOutput
  extends ListOpsMetadataResult,
    __MetadataBearer {}
declare const ListOpsMetadataCommand_base: {
  new (
    input: ListOpsMetadataCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListOpsMetadataCommandInput,
    ListOpsMetadataCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListOpsMetadataCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListOpsMetadataCommandInput,
    ListOpsMetadataCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListOpsMetadataCommand extends ListOpsMetadataCommand_base {
  protected static __types: {
    api: {
      input: ListOpsMetadataRequest;
      output: ListOpsMetadataResult;
    };
    sdk: {
      input: ListOpsMetadataCommandInput;
      output: ListOpsMetadataCommandOutput;
    };
  };
}
