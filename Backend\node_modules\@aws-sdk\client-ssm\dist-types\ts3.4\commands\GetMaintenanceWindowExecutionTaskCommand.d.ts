import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetMaintenanceWindowExecutionTaskRequest,
  GetMaintenanceWindowExecutionTaskResult,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface GetMaintenanceWindowExecutionTaskCommandInput
  extends GetMaintenanceWindowExecutionTaskRequest {}
export interface GetMaintenanceWindowExecutionTaskCommandOutput
  extends GetMaintenanceWindowExecutionTaskResult,
    __MetadataBearer {}
declare const GetMaintenanceWindowExecutionTaskCommand_base: {
  new (
    input: GetMaintenanceWindowExecutionTaskCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMaintenanceWindowExecutionTaskCommandInput,
    GetMaintenanceWindowExecutionTaskCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetMaintenanceWindowExecutionTaskCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMaintenanceWindowExecutionTaskCommandInput,
    GetMaintenanceWindowExecutionTaskCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetMaintenanceWindowExecutionTaskCommand extends GetMaintenanceWindowExecutionTaskCommand_base {
  protected static __types: {
    api: {
      input: GetMaintenanceWindowExecutionTaskRequest;
      output: GetMaintenanceWindowExecutionTaskResult;
    };
    sdk: {
      input: GetMaintenanceWindowExecutionTaskCommandInput;
      output: GetMaintenanceWindowExecutionTaskCommandOutput;
    };
  };
}
