import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import { GetParameterHistoryRequest, GetParameterHistoryResult } from "../models/models_1";
import { ServiceInputTypes, ServiceOutputTypes, SSMClientResolvedConfig } from "../SSMClient";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link GetParameterHistoryCommand}.
 */
export interface GetParameterHistoryCommandInput extends GetParameterHistoryRequest {
}
/**
 * @public
 *
 * The output of {@link GetParameterHistoryCommand}.
 */
export interface GetParameterHistoryCommandOutput extends GetParameterHistoryResult, __MetadataBearer {
}
declare const GetParameterHistoryCommand_base: {
    new (input: GetParameterHistoryCommandInput): import("@smithy/smithy-client").CommandImpl<GetParameterHistoryCommandInput, GetParameterHistoryCommandOutput, SSMClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: GetParameterHistoryCommandInput): import("@smithy/smithy-client").CommandImpl<GetParameterHistoryCommandInput, GetParameterHistoryCommandOutput, SSMClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Retrieves the history of all changes to a parameter.</p>
 *          <important>
 *             <p>If you change the KMS key alias for the KMS key used to encrypt a parameter,
 *     then you must also update the key alias the parameter uses to reference KMS. Otherwise,
 *      <code>GetParameterHistory</code> retrieves whatever the original key alias was
 *     referencing.</p>
 *          </important>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SSMClient, GetParameterHistoryCommand } from "@aws-sdk/client-ssm"; // ES Modules import
 * // const { SSMClient, GetParameterHistoryCommand } = require("@aws-sdk/client-ssm"); // CommonJS import
 * const client = new SSMClient(config);
 * const input = { // GetParameterHistoryRequest
 *   Name: "STRING_VALUE", // required
 *   WithDecryption: true || false,
 *   MaxResults: Number("int"),
 *   NextToken: "STRING_VALUE",
 * };
 * const command = new GetParameterHistoryCommand(input);
 * const response = await client.send(command);
 * // { // GetParameterHistoryResult
 * //   Parameters: [ // ParameterHistoryList
 * //     { // ParameterHistory
 * //       Name: "STRING_VALUE",
 * //       Type: "String" || "StringList" || "SecureString",
 * //       KeyId: "STRING_VALUE",
 * //       LastModifiedDate: new Date("TIMESTAMP"),
 * //       LastModifiedUser: "STRING_VALUE",
 * //       Description: "STRING_VALUE",
 * //       Value: "STRING_VALUE",
 * //       AllowedPattern: "STRING_VALUE",
 * //       Version: Number("long"),
 * //       Labels: [ // ParameterLabelList
 * //         "STRING_VALUE",
 * //       ],
 * //       Tier: "Standard" || "Advanced" || "Intelligent-Tiering",
 * //       Policies: [ // ParameterPolicyList
 * //         { // ParameterInlinePolicy
 * //           PolicyText: "STRING_VALUE",
 * //           PolicyType: "STRING_VALUE",
 * //           PolicyStatus: "STRING_VALUE",
 * //         },
 * //       ],
 * //       DataType: "STRING_VALUE",
 * //     },
 * //   ],
 * //   NextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param GetParameterHistoryCommandInput - {@link GetParameterHistoryCommandInput}
 * @returns {@link GetParameterHistoryCommandOutput}
 * @see {@link GetParameterHistoryCommandInput} for command's `input` shape.
 * @see {@link GetParameterHistoryCommandOutput} for command's `response` shape.
 * @see {@link SSMClientResolvedConfig | config} for SSMClient's `config` shape.
 *
 * @throws {@link InternalServerError} (server fault)
 *  <p>An error occurred on the server side.</p>
 *
 * @throws {@link InvalidKeyId} (client fault)
 *  <p>The query key ID isn't valid.</p>
 *
 * @throws {@link InvalidNextToken} (client fault)
 *  <p>The specified token isn't valid.</p>
 *
 * @throws {@link ParameterNotFound} (client fault)
 *  <p>The parameter couldn't be found. Verify the name and try again.</p>
 *          <note>
 *             <p>For the <code>DeleteParameter</code> and <code>GetParameter</code> actions, if the
 *     specified parameter doesn't exist, the <code>ParameterNotFound</code> exception is
 *      <i>not</i> recorded in CloudTrail event logs.</p>
 *          </note>
 *
 * @throws {@link SSMServiceException}
 * <p>Base exception class for all service exceptions from SSM service.</p>
 *
 *
 * @public
 */
export declare class GetParameterHistoryCommand extends GetParameterHistoryCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: GetParameterHistoryRequest;
            output: GetParameterHistoryResult;
        };
        sdk: {
            input: GetParameterHistoryCommandInput;
            output: GetParameterHistoryCommandOutput;
        };
    };
}
