"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/contact-us/route";
exports.ids = ["app/api/contact-us/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact-us%2Froute&page=%2Fapi%2Fcontact-us%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-us%2Froute.ts&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact-us%2Froute&page=%2Fapi%2Fcontact-us%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-us%2Froute.ts&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Krunal_Dubey_Documents_GitHub_mtl_nextjs_aws_site_Frontend_src_app_api_contact_us_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/contact-us/route.ts */ \"(rsc)/./src/app/api/contact-us/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/contact-us/route\",\n        pathname: \"/api/contact-us\",\n        filename: \"route\",\n        bundlePath: \"app/api/contact-us/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\GitHub\\\\mtl-nextjs-aws-site\\\\Frontend\\\\src\\\\app\\\\api\\\\contact-us\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Krunal_Dubey_Documents_GitHub_mtl_nextjs_aws_site_Frontend_src_app_api_contact_us_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/contact-us/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact-us%2Froute&page=%2Fapi%2Fcontact-us%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-us%2Froute.ts&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/contact-us/route.ts":
/*!*****************************************!*\
  !*** ./src/app/api/contact-us/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var common_sendDataToHubSpot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! common/sendDataToHubSpot */ \"(rsc)/./src/common/sendDataToHubSpot.ts\");\n/* harmony import */ var common_sendDataToSendGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! common/sendDataToSendGrid */ \"(rsc)/./src/common/sendDataToSendGrid.ts\");\n/* harmony import */ var common_currentTimestamp__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! common/currentTimestamp */ \"(rsc)/./src/common/currentTimestamp.ts\");\n/* harmony import */ var common_sendDataToSlack__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! common/sendDataToSlack */ \"(rsc)/./src/common/sendDataToSlack.ts\");\nconst dynamic = \"force-dynamic\";\n\n\n\n\n\nasync function POST(req) {\n    try {\n        const form_data = await req.json();\n        const formFields = [\n            {\n                name: \"firstname\",\n                value: form_data?.firstName ?? \"\"\n            },\n            {\n                name: \"lastname\",\n                value: form_data?.lastName ?? \"\"\n            },\n            {\n                name: \"email\",\n                value: form_data?.emailAddress ?? \"\"\n            },\n            {\n                name: \"phone\",\n                value: form_data?.phoneNumber ?? \"\"\n            },\n            {\n                name: \"how_did_you_hear_about_us_\",\n                value: form_data?.howDidYouHearAboutUs ?? \"\"\n            },\n            {\n                name: \"company\",\n                value: form_data?.companyName\n            },\n            {\n                name: \"message\",\n                value: form_data?.howCanWeHelpYou\n            },\n            {\n                name: \"utm_source\",\n                value: form_data?.utm_source ?? \"\"\n            },\n            {\n                name: \"utm_campaign\",\n                value: form_data?.utm_campaign ?? \"\"\n            },\n            {\n                name: \"utm_medium\",\n                value: form_data?.utm_medium ?? \"\"\n            },\n            {\n                name: \"clarity_link\",\n                value: form_data?.clarity ?? \"\"\n            },\n            {\n                name: \"source_url\",\n                value: form_data?.url ?? \"\"\n            },\n            {\n                name: \"referrer\",\n                value: form_data?.referrer ?? \"\"\n            },\n            {\n                name: \"ip_address\",\n                value: form_data?.ip_address ?? \"\"\n            },\n            {\n                name: \"city\",\n                value: form_data?.city ?? \"\"\n            },\n            {\n                name: \"country\",\n                value: form_data?.country ?? \"\"\n            },\n            {\n                name: \"ga_4_userid\",\n                value: form_data?.ga_4_userid\n            },\n            {\n                name: \"source\",\n                value: form_data?.secondary_source ?? \"HomePage\"\n            },\n            {\n                name: \"consent\",\n                value: form_data?.consent ?? \"\"\n            }\n        ];\n        const payload = {\n            fields: formFields,\n            context: {\n                pageUri: form_data?.url\n            },\n            headers: {\n                \"Content-Type\": \"application/json\",\n                Authorization: `Bearer ${\"pat-na2-d7414caf-1760-4ce7-9f9f-693adbdc87df\"}`\n            }\n        };\n        try {\n            //Send Data to HubSpot\n            const hubspotResponse = await (0,common_sendDataToHubSpot__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(form_data?.secondary_source, payload, \"59713666-6cfb-4bee-a8c4-805ace73e3bd\");\n            if (hubspotResponse?.status === 200 || hubspotResponse?.status === 201) {\n                //Send Data to SendGrid if HubSpot submission is successful !!!\n                const emailRes = await (0,common_sendDataToSendGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"<EMAIL>\", \"<EMAIL>\", form_data?.emailAddress, \"d-939cfe3e411546d880c6b4f48f91c1eb\", form_data);\n                //Send Data to success slack channel\n                await (0,common_sendDataToSlack__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(form_data, \"*******************************************************************************\");\n                //NECESSARY LOGS -> FOR DEBUGGING PURPOSE\n                console.log((0,common_currentTimestamp__WEBPACK_IMPORTED_MODULE_3__[\"default\"])());\n                console.log(\"Lead Data\", form_data);\n                console.log(\"HubSpot Response\", hubspotResponse);\n                console.log(\"SendGrid Email Response\", emailRes);\n                console.log(\"------------------------------------\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Form submitted successfully.\",\n                    hubspotResponse: hubspotResponse.message\n                }, {\n                    status: 200\n                });\n            } else {\n                console.error(\"HubSpot Error:\", hubspotResponse);\n                //If HubSpot POST API fails -> send (Error while sending form data to HubSpot) failure email\n                let formLeadData = form_data;\n                formLeadData.page_name = form_data?.secondary_source;\n                formLeadData.failed_source = \"Hubspot\";\n                const failureEmail = await (0,common_sendDataToSendGrid__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\"<EMAIL>\", \"<EMAIL>\", form_data?.emailAddress, \"d-1539bf8378c044c3a1787cf6ec9dbd51\", formLeadData);\n                // If HubSpot submission fails -> Send to a failure slack channel\n                await (0,common_sendDataToSlack__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(form_data, \"*******************************************************************************\", \"⚠️ HubSpot Form Submission Failed ⚠️\");\n                //Check if failure mail successfully sent or not\n                if (failureEmail.status) {\n                    console.error(`${form_data?.secondary_source} form, failure email send`);\n                } else {\n                    console.error(`${form_data?.secondary_source} form, failed to send failure email`);\n                }\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    message: \"Form submission failed.\",\n                    error: hubspotResponse?.error || \"Unknown error from HubSpot\"\n                }, {\n                    status: hubspotResponse?.status || 500\n                });\n            }\n        } catch (error) {\n            console.error(\"Error sending to HubSpot:\", error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                message: \"Internal server error while sending data to HubSpot\",\n                error: error.message || error\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error parsing request:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: \"Invalid request data\",\n            error: error.message || error\n        }, {\n            status: 400\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/contact-us/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/common/currentTimestamp.ts":
/*!****************************************!*\
  !*** ./src/common/currentTimestamp.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst date = new Date();\nconst currentTimestamp = ()=>{\n    const formatData = (input)=>{\n        if (input > 9) {\n            return input;\n        } else {\n            return `0${input}`;\n        }\n    };\n    let dd = formatData(date.getDate());\n    const monthNames = [\n        \"January\",\n        \"February\",\n        \"March\",\n        \"April\",\n        \"May\",\n        \"June\",\n        \"July\",\n        \"August\",\n        \"September\",\n        \"October\",\n        \"November\",\n        \"December\"\n    ];\n    let month = monthNames[date.getMonth()];\n    let yyyy = date.getFullYear();\n    let HH = formatData(date.getHours());\n    let MM = formatData(date.getMinutes());\n    let SS = formatData(date.getSeconds());\n    return `---> NEW LEAD ALERT : ${dd}/${month}/${yyyy} ${HH}:${MM}:${SS}`;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (currentTimestamp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/common/currentTimestamp.ts\n");

/***/ }),

/***/ "(rsc)/./src/common/sendDataToHubSpot.ts":
/*!*****************************************!*\
  !*** ./src/common/sendDataToHubSpot.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\nconst sendDataToHubspot = async (formPage, payload, formGuid)=>{\n    try {\n        const response = await axios.post(`https://api.hsforms.com/submissions/v3/integration/submit/${\"242083449\"}/${formGuid}`, payload);\n        return {\n            status: response.status,\n            message: `${formPage} form data sent to HubSpot successfully.`\n        };\n    } catch (error) {\n        return {\n            status: error.response?.status || 500,\n            error: `${formPage} error while sending data to HubSpot.`\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sendDataToHubspot);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tbW9uL3NlbmREYXRhVG9IdWJTcG90LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSxRQUFRQyxtQkFBT0EsQ0FBQztBQUV0QixNQUFNQyxvQkFBb0IsT0FBT0MsVUFBVUMsU0FBU0M7SUFDbEQsSUFBSTtRQUNGLE1BQU1DLFdBQVcsTUFBTU4sTUFBTU8sSUFBSSxDQUMvQixDQUFDLDBEQUEwRCxFQUFFQyxXQUF5QyxDQUFDLENBQUMsRUFBRUgsU0FBUyxDQUFDLEVBQ3BIRDtRQUdGLE9BQU87WUFDTE8sUUFBUUwsU0FBU0ssTUFBTTtZQUN2QkMsU0FBUyxDQUFDLEVBQUVULFNBQVMsd0NBQXdDLENBQUM7UUFDaEU7SUFDRixFQUFFLE9BQU9VLE9BQU87UUFDZCxPQUFPO1lBQ0xGLFFBQVFFLE1BQU1QLFFBQVEsRUFBRUssVUFBVTtZQUNsQ0UsT0FBTyxDQUFDLEVBQUVWLFNBQVMscUNBQXFDLENBQUM7UUFDM0Q7SUFDRjtBQUNGO0FBRUEsaUVBQWVELGlCQUFpQkEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL21hcnV0aS10ZWNoLy4vc3JjL2NvbW1vbi9zZW5kRGF0YVRvSHViU3BvdC50cz8wNmE3Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGF4aW9zID0gcmVxdWlyZSgnYXhpb3MnKTtcclxuXHJcbmNvbnN0IHNlbmREYXRhVG9IdWJzcG90ID0gYXN5bmMgKGZvcm1QYWdlLCBwYXlsb2FkLCBmb3JtR3VpZCkgPT4ge1xyXG4gIHRyeSB7XHJcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGF4aW9zLnBvc3QoXHJcbiAgICAgIGBodHRwczovL2FwaS5oc2Zvcm1zLmNvbS9zdWJtaXNzaW9ucy92My9pbnRlZ3JhdGlvbi9zdWJtaXQvJHtwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19IVUJTUE9UX1BPUlRBTF9JRH0vJHtmb3JtR3VpZH1gLFxyXG4gICAgICBwYXlsb2FkLFxyXG4gICAgKTtcclxuXHJcbiAgICByZXR1cm4ge1xyXG4gICAgICBzdGF0dXM6IHJlc3BvbnNlLnN0YXR1cywgLy8gUmV0dXJuIGFjdHVhbCBIVFRQIHN0YXR1cyBjb2RlXHJcbiAgICAgIG1lc3NhZ2U6IGAke2Zvcm1QYWdlfSBmb3JtIGRhdGEgc2VudCB0byBIdWJTcG90IHN1Y2Nlc3NmdWxseS5gLFxyXG4gICAgfTtcclxuICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgc3RhdHVzOiBlcnJvci5yZXNwb25zZT8uc3RhdHVzIHx8IDUwMCwgLy8gUmV0dXJuIGVycm9yIHN0YXR1cyBpZiBhdmFpbGFibGVcclxuICAgICAgZXJyb3I6IGAke2Zvcm1QYWdlfSBlcnJvciB3aGlsZSBzZW5kaW5nIGRhdGEgdG8gSHViU3BvdC5gLFxyXG4gICAgfTtcclxuICB9XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBzZW5kRGF0YVRvSHVic3BvdDtcclxuIl0sIm5hbWVzIjpbImF4aW9zIiwicmVxdWlyZSIsInNlbmREYXRhVG9IdWJzcG90IiwiZm9ybVBhZ2UiLCJwYXlsb2FkIiwiZm9ybUd1aWQiLCJyZXNwb25zZSIsInBvc3QiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfSFVCU1BPVF9QT1JUQUxfSUQiLCJzdGF0dXMiLCJtZXNzYWdlIiwiZXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/common/sendDataToHubSpot.ts\n");

/***/ }),

/***/ "(rsc)/./src/common/sendDataToSendGrid.ts":
/*!******************************************!*\
  !*** ./src/common/sendDataToSendGrid.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst axios = __webpack_require__(/*! axios */ \"(rsc)/./node_modules/axios/dist/node/axios.cjs\");\nconst sendDataToSendGrid = async (to, from, replyTo, templateId, dynamicData)=>{\n    let config = {\n        headers: {\n            \"Content-Type\": \"application/json\",\n            Authorization: \"Bearer \" + \"*********************************************************************\"\n        }\n    };\n    let mailTo = process.env.NEXT_PUBLIC_SECOND_RECIPENT ? [\n        {\n            email: to\n        },\n        {\n            email: process.env.NEXT_PUBLIC_SECOND_RECIPENT\n        }\n    ] : [\n        {\n            email: to\n        }\n    ];\n    const data = {\n        from: {\n            email: from\n        },\n        reply_to: {\n            email: replyTo\n        },\n        personalizations: [\n            {\n                to: mailTo,\n                dynamic_template_data: {\n                    lead: dynamicData\n                }\n            }\n        ],\n        template_id: templateId\n    };\n    try {\n        let result = await axios.post(\"https://api.sendgrid.com/v3/mail/send\", data, config);\n        if (result.status === 200 || result.status === 202) {\n            return {\n                status: true,\n                message: \"Sendgrid API message: Email sent successfully.\"\n            };\n        } else {\n            throw \"Error\";\n        }\n    } catch (error) {\n        console.error(\"--------error\", error);\n        return {\n            status: false,\n            error: `Sendgrid API message: Error while sending email.`\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sendDataToSendGrid);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/common/sendDataToSendGrid.ts\n");

/***/ }),

/***/ "(rsc)/./src/common/sendDataToSlack.ts":
/*!***************************************!*\
  !*** ./src/common/sendDataToSlack.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst sendToSlack = async (formData, webhookUrl = process.env.NEXT_PUBLIC_SLACK_WEBHOOK_URL, extraText = \"\")=>{\n    try {\n        if (!webhookUrl) {\n            console.error(\"Slack webhook URL is not defined.\");\n            return;\n        }\n        const isAIReadinessForm = formData.secondary_source === \"AIReadiness\";\n        const messageTitle = extraText ? \"\\uD83D\\uDEA8 *Form Submission Failed!* ⚠️\" : isAIReadinessForm ? \"\\uD83E\\uDD16 *New AI Readiness Submission!* \\uD83E\\uDD16\" : \"\\uD83D\\uDE80 *New Form Submission!* \\uD83D\\uDE80\";\n        const aiFields = `\r\n        • First Name: ${formData.firstName || \"N/A\"}\r\n        • Last Name: ${formData.lastName || \"N/A\"}\r\n        • Email Address: ${formData.emailAddress || \"N/A\"}\r\n        • Phone Number: ${formData.phoneNumber || \"N/A\"}\r\n        • Company Name: ${formData.companyName || \"N/A\"}\r\n        • UTM Campaign: ${formData.utm_campaign || \"N/A\"}\r\n        • UTM Medium: ${formData.utm_medium || \"N/A\"}\r\n        • UTM Source: ${formData.utm_source || \"N/A\"}\r\n        • GA 4 User ID: ${formData.ga_4_userid || \"N/A\"}\r\n        • IP Address: ${formData.ip_address || \"N/A\"}\r\n        • City: ${formData.city || \"N/A\"}\r\n        • Country: ${formData.country || \"N/A\"}\r\n        • Referrer: ${formData.referrer || \"N/A\"}\r\n        • Clarity: ${formData.clarity || \"N/A\"}\r\n        • Page URL: ${formData.url || \"N/A\"}\r\n        • Consent: ${formData.consent ? \"Yes\" : \"No\"}\r\n        • Do you have clearly defined business objectives and goals for the AI project? : ${formData.do_you_have_clearly_defined_business_objectives_and_goals_for_the_ai_project_ || \"N/A\"}\r\n        • How receptive is your Leadership Team to embracing the changes brought about by AI? : ${formData.how_receptive_is_your_leadership_team_to_embracing_the_changes_brought_about_by_ai_ || \"N/A\"}\r\n        • Do you have budget allocated for your AI project? : ${formData.do_you_have_budget_allocated_for_your_ai_project_ || \"N/A\"}\r\n        • Do you have a robust data infrastructure for storage, retrieval, and processing? : ${formData.do_you_have_a_robust_data_infrastructure_for_storage__retrieval__and_processing_ || \"N/A\"}\r\n        • Which of the below DB tools do you currently use? : ${formData.which_of_the_below_db_tools_do_you_currently_use_ || \"N/A\"}\r\n        • Is the relevant data for the AI project available and accessible? : ${formData.is_the_relevant_data_for_the_ai_project_available_and_accessible_ || \"N/A\"}\r\n        • Do you have access to necessary computing resources (CPU, GPU, cloud services)? : ${formData.do_you_have_access_to_necessary_computing_resources__cpu__gpu__cloud_services__ || \"N/A\"}\r\n        • How would you rate your organization's current IT infrastructure in terms of scalability and flexibility to accommodate the evolving computational needs of AI projects? : ${formData.how_would_you_rate_your_organization_s_current_it_infrastructure_in_terms_of_scalability_and_flexib || \"N/A\"}\r\n        • Does the team have the expertise in data science, machine learning, and AI? : ${formData.does_the_team_have_the_expertise_in_data_science__machine_learning__and_ai_ || \"N/A\"}\r\n        • Do you have systems in place to monitor the performance and accuracy of AI models post-deployment? : ${formData.do_you_have_systems_in_place_to_monitor_the_performance_and_accuracy_of_ai_models_post_deployment_ || \"N/A\"}\r\n        • Do you have risk management strategies in place for the AI project? : ${formData.do_you_have_risk_management_strategies_in_place_for_the_ai_project_ || \"N/A\"}\r\n        • Do you have a process in place to measure the impact of the deployment of AI / AI-powered solutions? : ${formData.do_you_have_a_process_in_place_to_measure_the_impact_of_the_deployment_of_ai___ai_powered_solutions || \"N/A\"}\r\n\r\n        • Strategy & Leadership: ${formData.strategy___leadership || \"0\"} %\r\n        • Data Readiness & Infrastructure: ${formData.data_readiness___infrastructure || \"0\"} %\r\n        • Talent & Skills: ${formData.talent___skills || \"N/A\"} %\r\n        • Execution & Monitoring: ${formData.execution___monitoring || \"0\"} %\r\n        • Impact Evaluation: ${formData.impact_evaliation || \"0\"} %\r\n        • Average Score: ${formData.average_of_all_score || \"0\"} %\r\n        `;\n        const contactFields = `\r\n        • *First Name:* ${formData.firstName || \"N/A\"}\r\n        • *Last Name:* ${formData.lastName || \"N/A\"}\r\n        • *Email:* ${formData.emailAddress || \"N/A\"}\r\n        • *Phone:* ${formData.phoneNumber || \"N/A\"}\r\n        • *Company:* ${formData.companyName || \"N/A\"}\r\n        • *How Did You Hear About Us?* ${formData.howDidYouHearAboutUs || \"N/A\"}\r\n        • *How Can We Help?* ${formData.howCanWeHelpYou || \"N/A\"}\r\n        • *UTM Campaign:* ${formData.utm_campaign || \"N/A\"}\r\n        • *UTM Medium:* ${formData.utm_medium || \"N/A\"}\r\n        • *UTM Source:* ${formData.utm_source || \"N/A\"}\r\n        • *IP Address:* ${formData.ip_address || \"N/A\"}\r\n        • *GA4 User ID:* ${formData.ga_4_userid || \"N/A\"}\r\n        • *City:* ${formData.city || \"N/A\"}\r\n        • *Country:* ${formData.country || \"N/A\"}\r\n        • *Secondary Source:* ${formData.secondary_source || \"N/A\"}\r\n        • *Clarity:* ${formData.clarity || \"N/A\"}\r\n        • *Page URL:* ${formData.url || \"N/A\"}\r\n        • *Referrer:* ${formData.referrer || \"N/A\"}\r\n        • *Consent:* ${formData.consent ? \"Yes\" : \"No\"} `;\n        const message = {\n            text: messageTitle,\n            blocks: [\n                {\n                    type: \"section\",\n                    text: {\n                        type: \"mrkdwn\",\n                        text: `${messageTitle}\\n\\n${isAIReadinessForm ? aiFields : contactFields}`\n                    }\n                },\n                {\n                    type: \"divider\"\n                }\n            ]\n        };\n        const response = await fetch(webhookUrl, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify(message)\n        });\n        if (!response.ok) {\n            console.error(\"Failed to send Slack notification:\", await response.text());\n        }\n    } catch (error) {\n        console.error(\"Error sending message to Slack:\", error);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sendToSlack);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/common/sendDataToSlack.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/asynckit","vendor-chunks/debug","vendor-chunks/mime-db","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/axios","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/has-flag","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcontact-us%2Froute&page=%2Fapi%2Fcontact-us%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcontact-us%2Froute.ts&appDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CKrunal%20Dubey%5CDocuments%5CGitHub%5Cmtl-nextjs-aws-site%5CFrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();