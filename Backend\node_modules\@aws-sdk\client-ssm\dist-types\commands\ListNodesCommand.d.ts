import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { ListNodesRequest, ListNodesResult } from "../models/models_1";
import { ServiceInputTypes, ServiceOutputTypes, SSMClientResolvedConfig } from "../SSMClient";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link ListNodesCommand}.
 */
export interface ListNodesCommandInput extends ListNodesRequest {
}
/**
 * @public
 *
 * The output of {@link ListNodesCommand}.
 */
export interface ListNodesCommandOutput extends ListNodesResult, __MetadataBearer {
}
declare const ListNodesCommand_base: {
    new (input: ListNodesCommandInput): import("@smithy/smithy-client").CommandImpl<ListNodesCommandInput, ListNodesCommandOutput, SSMClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (...[input]: [] | [ListNodesCommandInput]): import("@smithy/smithy-client").CommandImpl<ListNodesCommandInput, ListNodesCommandOutput, SSMClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Takes in filters and returns a list of managed nodes matching the filter criteria.</p>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { SSMClient, ListNodesCommand } from "@aws-sdk/client-ssm"; // ES Modules import
 * // const { SSMClient, ListNodesCommand } = require("@aws-sdk/client-ssm"); // CommonJS import
 * const client = new SSMClient(config);
 * const input = { // ListNodesRequest
 *   SyncName: "STRING_VALUE",
 *   Filters: [ // NodeFilterList
 *     { // NodeFilter
 *       Key: "AgentType" || "AgentVersion" || "ComputerName" || "InstanceId" || "InstanceStatus" || "IpAddress" || "ManagedStatus" || "PlatformName" || "PlatformType" || "PlatformVersion" || "ResourceType" || "OrganizationalUnitId" || "OrganizationalUnitPath" || "Region" || "AccountId", // required
 *       Values: [ // NodeFilterValueList // required
 *         "STRING_VALUE",
 *       ],
 *       Type: "Equal" || "NotEqual" || "BeginWith",
 *     },
 *   ],
 *   NextToken: "STRING_VALUE",
 *   MaxResults: Number("int"),
 * };
 * const command = new ListNodesCommand(input);
 * const response = await client.send(command);
 * // { // ListNodesResult
 * //   Nodes: [ // NodeList
 * //     { // Node
 * //       CaptureTime: new Date("TIMESTAMP"),
 * //       Id: "STRING_VALUE",
 * //       Owner: { // NodeOwnerInfo
 * //         AccountId: "STRING_VALUE",
 * //         OrganizationalUnitId: "STRING_VALUE",
 * //         OrganizationalUnitPath: "STRING_VALUE",
 * //       },
 * //       Region: "STRING_VALUE",
 * //       NodeType: { // NodeType Union: only one key present
 * //         Instance: { // InstanceInfo
 * //           AgentType: "STRING_VALUE",
 * //           AgentVersion: "STRING_VALUE",
 * //           ComputerName: "STRING_VALUE",
 * //           InstanceStatus: "STRING_VALUE",
 * //           IpAddress: "STRING_VALUE",
 * //           ManagedStatus: "All" || "Managed" || "Unmanaged",
 * //           PlatformType: "Windows" || "Linux" || "MacOS",
 * //           PlatformName: "STRING_VALUE",
 * //           PlatformVersion: "STRING_VALUE",
 * //           ResourceType: "ManagedInstance" || "EC2Instance",
 * //         },
 * //       },
 * //     },
 * //   ],
 * //   NextToken: "STRING_VALUE",
 * // };
 *
 * ```
 *
 * @param ListNodesCommandInput - {@link ListNodesCommandInput}
 * @returns {@link ListNodesCommandOutput}
 * @see {@link ListNodesCommandInput} for command's `input` shape.
 * @see {@link ListNodesCommandOutput} for command's `response` shape.
 * @see {@link SSMClientResolvedConfig | config} for SSMClient's `config` shape.
 *
 * @throws {@link InternalServerError} (server fault)
 *  <p>An error occurred on the server side.</p>
 *
 * @throws {@link InvalidFilter} (client fault)
 *  <p>The filter name isn't valid. Verify that you entered the correct name and try again.</p>
 *
 * @throws {@link InvalidNextToken} (client fault)
 *  <p>The specified token isn't valid.</p>
 *
 * @throws {@link ResourceDataSyncNotFoundException} (client fault)
 *  <p>The specified sync name wasn't found.</p>
 *
 * @throws {@link UnsupportedOperationException} (client fault)
 *  <p>This operation is not supported for the current account. You must first enable the Systems Manager integrated experience in your account.</p>
 *
 * @throws {@link SSMServiceException}
 * <p>Base exception class for all service exceptions from SSM service.</p>
 *
 *
 * @public
 */
export declare class ListNodesCommand extends ListNodesCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: ListNodesRequest;
            output: ListNodesResult;
        };
        sdk: {
            input: ListNodesCommandInput;
            output: ListNodesCommandOutput;
        };
    };
}
