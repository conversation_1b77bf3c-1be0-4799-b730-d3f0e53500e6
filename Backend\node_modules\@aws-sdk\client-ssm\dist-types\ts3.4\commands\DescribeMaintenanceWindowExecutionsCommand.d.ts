import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeMaintenanceWindowExecutionsRequest,
  DescribeMaintenanceWindowExecutionsResult,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeMaintenanceWindowExecutionsCommandInput
  extends DescribeMaintenanceWindowExecutionsRequest {}
export interface DescribeMaintenanceWindowExecutionsCommandOutput
  extends DescribeMaintenanceWindowExecutionsResult,
    __MetadataBearer {}
declare const DescribeMaintenanceWindowExecutionsCommand_base: {
  new (
    input: DescribeMaintenanceWindowExecutionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeMaintenanceWindowExecutionsCommandInput,
    DescribeMaintenanceWindowExecutionsCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeMaintenanceWindowExecutionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeMaintenanceWindowExecutionsCommandInput,
    DescribeMaintenanceWindowExecutionsCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeMaintenanceWindowExecutionsCommand extends DescribeMaintenanceWindowExecutionsCommand_base {
  protected static __types: {
    api: {
      input: DescribeMaintenanceWindowExecutionsRequest;
      output: DescribeMaintenanceWindowExecutionsResult;
    };
    sdk: {
      input: DescribeMaintenanceWindowExecutionsCommandInput;
      output: DescribeMaintenanceWindowExecutionsCommandOutput;
    };
  };
}
