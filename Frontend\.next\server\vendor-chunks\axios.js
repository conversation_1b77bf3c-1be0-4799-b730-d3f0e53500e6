"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/axios";
exports.ids = ["vendor-chunks/axios"];
exports.modules = {

/***/ "(rsc)/./node_modules/axios/dist/node/axios.cjs":
/*!************************************************!*\
  !*** ./node_modules/axios/dist/node/axios.cjs ***!
  \************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Axios v1.7.7 Copyright (c) 2024 Matt Zabriskie and contributors\n\nconst FormData$1 = __webpack_require__(/*! form-data */ \"(rsc)/./node_modules/form-data/lib/form_data.js\");\nconst url = __webpack_require__(/*! url */ \"url\");\nconst proxyFromEnv = __webpack_require__(/*! proxy-from-env */ \"(rsc)/./node_modules/proxy-from-env/index.js\");\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst util = __webpack_require__(/*! util */ \"util\");\nconst followRedirects = __webpack_require__(/*! follow-redirects */ \"(rsc)/./node_modules/follow-redirects/index.js\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst stream = __webpack_require__(/*! stream */ \"stream\");\nconst events = __webpack_require__(/*! events */ \"events\");\nfunction _interopDefaultLegacy(e) {\n    return e && typeof e === \"object\" && \"default\" in e ? e : {\n        \"default\": e\n    };\n}\nconst FormData__default = /*#__PURE__*/ _interopDefaultLegacy(FormData$1);\nconst url__default = /*#__PURE__*/ _interopDefaultLegacy(url);\nconst http__default = /*#__PURE__*/ _interopDefaultLegacy(http);\nconst https__default = /*#__PURE__*/ _interopDefaultLegacy(https);\nconst util__default = /*#__PURE__*/ _interopDefaultLegacy(util);\nconst followRedirects__default = /*#__PURE__*/ _interopDefaultLegacy(followRedirects);\nconst zlib__default = /*#__PURE__*/ _interopDefaultLegacy(zlib);\nconst stream__default = /*#__PURE__*/ _interopDefaultLegacy(stream);\nfunction bind(fn, thisArg) {\n    return function wrap() {\n        return fn.apply(thisArg, arguments);\n    };\n}\n// utils is a library of generic helper functions non-specific to axios\nconst { toString } = Object.prototype;\nconst { getPrototypeOf } = Object;\nconst kindOf = ((cache)=>(thing)=>{\n        const str = toString.call(thing);\n        return cache[str] || (cache[str] = str.slice(8, -1).toLowerCase());\n    })(Object.create(null));\nconst kindOfTest = (type)=>{\n    type = type.toLowerCase();\n    return (thing)=>kindOf(thing) === type;\n};\nconst typeOfTest = (type)=>(thing)=>typeof thing === type;\n/**\n * Determine if a value is an Array\n *\n * @param {Object} val The value to test\n *\n * @returns {boolean} True if value is an Array, otherwise false\n */ const { isArray } = Array;\n/**\n * Determine if a value is undefined\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if the value is undefined, otherwise false\n */ const isUndefined = typeOfTest(\"undefined\");\n/**\n * Determine if a value is a Buffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Buffer, otherwise false\n */ function isBuffer(val) {\n    return val !== null && !isUndefined(val) && val.constructor !== null && !isUndefined(val.constructor) && isFunction(val.constructor.isBuffer) && val.constructor.isBuffer(val);\n}\n/**\n * Determine if a value is an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is an ArrayBuffer, otherwise false\n */ const isArrayBuffer = kindOfTest(\"ArrayBuffer\");\n/**\n * Determine if a value is a view on an ArrayBuffer\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a view on an ArrayBuffer, otherwise false\n */ function isArrayBufferView(val) {\n    let result;\n    if (typeof ArrayBuffer !== \"undefined\" && ArrayBuffer.isView) {\n        result = ArrayBuffer.isView(val);\n    } else {\n        result = val && val.buffer && isArrayBuffer(val.buffer);\n    }\n    return result;\n}\n/**\n * Determine if a value is a String\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a String, otherwise false\n */ const isString = typeOfTest(\"string\");\n/**\n * Determine if a value is a Function\n *\n * @param {*} val The value to test\n * @returns {boolean} True if value is a Function, otherwise false\n */ const isFunction = typeOfTest(\"function\");\n/**\n * Determine if a value is a Number\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Number, otherwise false\n */ const isNumber = typeOfTest(\"number\");\n/**\n * Determine if a value is an Object\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an Object, otherwise false\n */ const isObject = (thing)=>thing !== null && typeof thing === \"object\";\n/**\n * Determine if a value is a Boolean\n *\n * @param {*} thing The value to test\n * @returns {boolean} True if value is a Boolean, otherwise false\n */ const isBoolean = (thing)=>thing === true || thing === false;\n/**\n * Determine if a value is a plain Object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a plain Object, otherwise false\n */ const isPlainObject = (val)=>{\n    if (kindOf(val) !== \"object\") {\n        return false;\n    }\n    const prototype = getPrototypeOf(val);\n    return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in val) && !(Symbol.iterator in val);\n};\n/**\n * Determine if a value is a Date\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Date, otherwise false\n */ const isDate = kindOfTest(\"Date\");\n/**\n * Determine if a value is a File\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */ const isFile = kindOfTest(\"File\");\n/**\n * Determine if a value is a Blob\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Blob, otherwise false\n */ const isBlob = kindOfTest(\"Blob\");\n/**\n * Determine if a value is a FileList\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a File, otherwise false\n */ const isFileList = kindOfTest(\"FileList\");\n/**\n * Determine if a value is a Stream\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a Stream, otherwise false\n */ const isStream = (val)=>isObject(val) && isFunction(val.pipe);\n/**\n * Determine if a value is a FormData\n *\n * @param {*} thing The value to test\n *\n * @returns {boolean} True if value is an FormData, otherwise false\n */ const isFormData = (thing)=>{\n    let kind;\n    return thing && (typeof FormData === \"function\" && thing instanceof FormData || isFunction(thing.append) && ((kind = kindOf(thing)) === \"formdata\" || // detect form-data instance\n    kind === \"object\" && isFunction(thing.toString) && thing.toString() === \"[object FormData]\"));\n};\n/**\n * Determine if a value is a URLSearchParams object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a URLSearchParams object, otherwise false\n */ const isURLSearchParams = kindOfTest(\"URLSearchParams\");\nconst [isReadableStream, isRequest, isResponse, isHeaders] = [\n    \"ReadableStream\",\n    \"Request\",\n    \"Response\",\n    \"Headers\"\n].map(kindOfTest);\n/**\n * Trim excess whitespace off the beginning and end of a string\n *\n * @param {String} str The String to trim\n *\n * @returns {String} The String freed of excess whitespace\n */ const trim = (str)=>str.trim ? str.trim() : str.replace(/^[\\s\\uFEFF\\xA0]+|[\\s\\uFEFF\\xA0]+$/g, \"\");\n/**\n * Iterate over an Array or an Object invoking a function for each item.\n *\n * If `obj` is an Array callback will be called passing\n * the value, index, and complete array for each item.\n *\n * If 'obj' is an Object callback will be called passing\n * the value, key, and complete object for each property.\n *\n * @param {Object|Array} obj The object to iterate\n * @param {Function} fn The callback to invoke for each item\n *\n * @param {Boolean} [allOwnKeys = false]\n * @returns {any}\n */ function forEach(obj, fn, { allOwnKeys = false } = {}) {\n    // Don't bother if no value provided\n    if (obj === null || typeof obj === \"undefined\") {\n        return;\n    }\n    let i;\n    let l;\n    // Force an array if not already something iterable\n    if (typeof obj !== \"object\") {\n        /*eslint no-param-reassign:0*/ obj = [\n            obj\n        ];\n    }\n    if (isArray(obj)) {\n        // Iterate over array values\n        for(i = 0, l = obj.length; i < l; i++){\n            fn.call(null, obj[i], i, obj);\n        }\n    } else {\n        // Iterate over object keys\n        const keys = allOwnKeys ? Object.getOwnPropertyNames(obj) : Object.keys(obj);\n        const len = keys.length;\n        let key;\n        for(i = 0; i < len; i++){\n            key = keys[i];\n            fn.call(null, obj[key], key, obj);\n        }\n    }\n}\nfunction findKey(obj, key) {\n    key = key.toLowerCase();\n    const keys = Object.keys(obj);\n    let i = keys.length;\n    let _key;\n    while(i-- > 0){\n        _key = keys[i];\n        if (key === _key.toLowerCase()) {\n            return _key;\n        }\n    }\n    return null;\n}\nconst _global = (()=>{\n    /*eslint no-undef:0*/ if (typeof globalThis !== \"undefined\") return globalThis;\n    return typeof self !== \"undefined\" ? self :  false ? 0 : global;\n})();\nconst isContextDefined = (context)=>!isUndefined(context) && context !== _global;\n/**\n * Accepts varargs expecting each argument to be an object, then\n * immutably merges the properties of each object and returns result.\n *\n * When multiple objects contain the same key the later object in\n * the arguments list will take precedence.\n *\n * Example:\n *\n * ```js\n * var result = merge({foo: 123}, {foo: 456});\n * console.log(result.foo); // outputs 456\n * ```\n *\n * @param {Object} obj1 Object to merge\n *\n * @returns {Object} Result of all merge properties\n */ function merge() {\n    const { caseless } = isContextDefined(this) && this || {};\n    const result = {};\n    const assignValue = (val, key)=>{\n        const targetKey = caseless && findKey(result, key) || key;\n        if (isPlainObject(result[targetKey]) && isPlainObject(val)) {\n            result[targetKey] = merge(result[targetKey], val);\n        } else if (isPlainObject(val)) {\n            result[targetKey] = merge({}, val);\n        } else if (isArray(val)) {\n            result[targetKey] = val.slice();\n        } else {\n            result[targetKey] = val;\n        }\n    };\n    for(let i = 0, l = arguments.length; i < l; i++){\n        arguments[i] && forEach(arguments[i], assignValue);\n    }\n    return result;\n}\n/**\n * Extends object a by mutably adding to it the properties of object b.\n *\n * @param {Object} a The object to be extended\n * @param {Object} b The object to copy properties from\n * @param {Object} thisArg The object to bind function to\n *\n * @param {Boolean} [allOwnKeys]\n * @returns {Object} The resulting value of object a\n */ const extend = (a, b, thisArg, { allOwnKeys } = {})=>{\n    forEach(b, (val, key)=>{\n        if (thisArg && isFunction(val)) {\n            a[key] = bind(val, thisArg);\n        } else {\n            a[key] = val;\n        }\n    }, {\n        allOwnKeys\n    });\n    return a;\n};\n/**\n * Remove byte order marker. This catches EF BB BF (the UTF-8 BOM)\n *\n * @param {string} content with BOM\n *\n * @returns {string} content value without BOM\n */ const stripBOM = (content)=>{\n    if (content.charCodeAt(0) === 0xFEFF) {\n        content = content.slice(1);\n    }\n    return content;\n};\n/**\n * Inherit the prototype methods from one constructor into another\n * @param {function} constructor\n * @param {function} superConstructor\n * @param {object} [props]\n * @param {object} [descriptors]\n *\n * @returns {void}\n */ const inherits = (constructor, superConstructor, props, descriptors)=>{\n    constructor.prototype = Object.create(superConstructor.prototype, descriptors);\n    constructor.prototype.constructor = constructor;\n    Object.defineProperty(constructor, \"super\", {\n        value: superConstructor.prototype\n    });\n    props && Object.assign(constructor.prototype, props);\n};\n/**\n * Resolve object with deep prototype chain to a flat object\n * @param {Object} sourceObj source object\n * @param {Object} [destObj]\n * @param {Function|Boolean} [filter]\n * @param {Function} [propFilter]\n *\n * @returns {Object}\n */ const toFlatObject = (sourceObj, destObj, filter, propFilter)=>{\n    let props;\n    let i;\n    let prop;\n    const merged = {};\n    destObj = destObj || {};\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    if (sourceObj == null) return destObj;\n    do {\n        props = Object.getOwnPropertyNames(sourceObj);\n        i = props.length;\n        while(i-- > 0){\n            prop = props[i];\n            if ((!propFilter || propFilter(prop, sourceObj, destObj)) && !merged[prop]) {\n                destObj[prop] = sourceObj[prop];\n                merged[prop] = true;\n            }\n        }\n        sourceObj = filter !== false && getPrototypeOf(sourceObj);\n    }while (sourceObj && (!filter || filter(sourceObj, destObj)) && sourceObj !== Object.prototype);\n    return destObj;\n};\n/**\n * Determines whether a string ends with the characters of a specified string\n *\n * @param {String} str\n * @param {String} searchString\n * @param {Number} [position= 0]\n *\n * @returns {boolean}\n */ const endsWith = (str, searchString, position)=>{\n    str = String(str);\n    if (position === undefined || position > str.length) {\n        position = str.length;\n    }\n    position -= searchString.length;\n    const lastIndex = str.indexOf(searchString, position);\n    return lastIndex !== -1 && lastIndex === position;\n};\n/**\n * Returns new array from array like object or null if failed\n *\n * @param {*} [thing]\n *\n * @returns {?Array}\n */ const toArray = (thing)=>{\n    if (!thing) return null;\n    if (isArray(thing)) return thing;\n    let i = thing.length;\n    if (!isNumber(i)) return null;\n    const arr = new Array(i);\n    while(i-- > 0){\n        arr[i] = thing[i];\n    }\n    return arr;\n};\n/**\n * Checking if the Uint8Array exists and if it does, it returns a function that checks if the\n * thing passed in is an instance of Uint8Array\n *\n * @param {TypedArray}\n *\n * @returns {Array}\n */ // eslint-disable-next-line func-names\nconst isTypedArray = ((TypedArray)=>{\n    // eslint-disable-next-line func-names\n    return (thing)=>{\n        return TypedArray && thing instanceof TypedArray;\n    };\n})(typeof Uint8Array !== \"undefined\" && getPrototypeOf(Uint8Array));\n/**\n * For each entry in the object, call the function with the key and value.\n *\n * @param {Object<any, any>} obj - The object to iterate over.\n * @param {Function} fn - The function to call for each entry.\n *\n * @returns {void}\n */ const forEachEntry = (obj, fn)=>{\n    const generator = obj && obj[Symbol.iterator];\n    const iterator = generator.call(obj);\n    let result;\n    while((result = iterator.next()) && !result.done){\n        const pair = result.value;\n        fn.call(obj, pair[0], pair[1]);\n    }\n};\n/**\n * It takes a regular expression and a string, and returns an array of all the matches\n *\n * @param {string} regExp - The regular expression to match against.\n * @param {string} str - The string to search.\n *\n * @returns {Array<boolean>}\n */ const matchAll = (regExp, str)=>{\n    let matches;\n    const arr = [];\n    while((matches = regExp.exec(str)) !== null){\n        arr.push(matches);\n    }\n    return arr;\n};\n/* Checking if the kindOfTest function returns true when passed an HTMLFormElement. */ const isHTMLForm = kindOfTest(\"HTMLFormElement\");\nconst toCamelCase = (str)=>{\n    return str.toLowerCase().replace(/[-_\\s]([a-z\\d])(\\w*)/g, function replacer(m, p1, p2) {\n        return p1.toUpperCase() + p2;\n    });\n};\n/* Creating a function that will check if an object has a property. */ const hasOwnProperty = (({ hasOwnProperty })=>(obj, prop)=>hasOwnProperty.call(obj, prop))(Object.prototype);\n/**\n * Determine if a value is a RegExp object\n *\n * @param {*} val The value to test\n *\n * @returns {boolean} True if value is a RegExp object, otherwise false\n */ const isRegExp = kindOfTest(\"RegExp\");\nconst reduceDescriptors = (obj, reducer)=>{\n    const descriptors = Object.getOwnPropertyDescriptors(obj);\n    const reducedDescriptors = {};\n    forEach(descriptors, (descriptor, name)=>{\n        let ret;\n        if ((ret = reducer(descriptor, name, obj)) !== false) {\n            reducedDescriptors[name] = ret || descriptor;\n        }\n    });\n    Object.defineProperties(obj, reducedDescriptors);\n};\n/**\n * Makes all methods read-only\n * @param {Object} obj\n */ const freezeMethods = (obj)=>{\n    reduceDescriptors(obj, (descriptor, name)=>{\n        // skip restricted props in strict mode\n        if (isFunction(obj) && [\n            \"arguments\",\n            \"caller\",\n            \"callee\"\n        ].indexOf(name) !== -1) {\n            return false;\n        }\n        const value = obj[name];\n        if (!isFunction(value)) return;\n        descriptor.enumerable = false;\n        if (\"writable\" in descriptor) {\n            descriptor.writable = false;\n            return;\n        }\n        if (!descriptor.set) {\n            descriptor.set = ()=>{\n                throw Error(\"Can not rewrite read-only method '\" + name + \"'\");\n            };\n        }\n    });\n};\nconst toObjectSet = (arrayOrString, delimiter)=>{\n    const obj = {};\n    const define = (arr)=>{\n        arr.forEach((value)=>{\n            obj[value] = true;\n        });\n    };\n    isArray(arrayOrString) ? define(arrayOrString) : define(String(arrayOrString).split(delimiter));\n    return obj;\n};\nconst noop = ()=>{};\nconst toFiniteNumber = (value, defaultValue)=>{\n    return value != null && Number.isFinite(value = +value) ? value : defaultValue;\n};\nconst ALPHA = \"abcdefghijklmnopqrstuvwxyz\";\nconst DIGIT = \"0123456789\";\nconst ALPHABET = {\n    DIGIT,\n    ALPHA,\n    ALPHA_DIGIT: ALPHA + ALPHA.toUpperCase() + DIGIT\n};\nconst generateString = (size = 16, alphabet = ALPHABET.ALPHA_DIGIT)=>{\n    let str = \"\";\n    const { length } = alphabet;\n    while(size--){\n        str += alphabet[Math.random() * length | 0];\n    }\n    return str;\n};\n/**\n * If the thing is a FormData object, return true, otherwise return false.\n *\n * @param {unknown} thing - The thing to check.\n *\n * @returns {boolean}\n */ function isSpecCompliantForm(thing) {\n    return !!(thing && isFunction(thing.append) && thing[Symbol.toStringTag] === \"FormData\" && thing[Symbol.iterator]);\n}\nconst toJSONObject = (obj)=>{\n    const stack = new Array(10);\n    const visit = (source, i)=>{\n        if (isObject(source)) {\n            if (stack.indexOf(source) >= 0) {\n                return;\n            }\n            if (!(\"toJSON\" in source)) {\n                stack[i] = source;\n                const target = isArray(source) ? [] : {};\n                forEach(source, (value, key)=>{\n                    const reducedValue = visit(value, i + 1);\n                    !isUndefined(reducedValue) && (target[key] = reducedValue);\n                });\n                stack[i] = undefined;\n                return target;\n            }\n        }\n        return source;\n    };\n    return visit(obj, 0);\n};\nconst isAsyncFn = kindOfTest(\"AsyncFunction\");\nconst isThenable = (thing)=>thing && (isObject(thing) || isFunction(thing)) && isFunction(thing.then) && isFunction(thing.catch);\n// original code\n// https://github.com/DigitalBrainJS/AxiosPromise/blob/16deab13710ec09779922131f3fa5954320f83ab/lib/utils.js#L11-L34\nconst _setImmediate = ((setImmediateSupported, postMessageSupported)=>{\n    if (setImmediateSupported) {\n        return setImmediate;\n    }\n    return postMessageSupported ? ((token, callbacks)=>{\n        _global.addEventListener(\"message\", ({ source, data })=>{\n            if (source === _global && data === token) {\n                callbacks.length && callbacks.shift()();\n            }\n        }, false);\n        return (cb)=>{\n            callbacks.push(cb);\n            _global.postMessage(token, \"*\");\n        };\n    })(`axios@${Math.random()}`, []) : (cb)=>setTimeout(cb);\n})(typeof setImmediate === \"function\", isFunction(_global.postMessage));\nconst asap = typeof queueMicrotask !== \"undefined\" ? queueMicrotask.bind(_global) : typeof process !== \"undefined\" && process.nextTick || _setImmediate;\n// *********************\nconst utils$1 = {\n    isArray,\n    isArrayBuffer,\n    isBuffer,\n    isFormData,\n    isArrayBufferView,\n    isString,\n    isNumber,\n    isBoolean,\n    isObject,\n    isPlainObject,\n    isReadableStream,\n    isRequest,\n    isResponse,\n    isHeaders,\n    isUndefined,\n    isDate,\n    isFile,\n    isBlob,\n    isRegExp,\n    isFunction,\n    isStream,\n    isURLSearchParams,\n    isTypedArray,\n    isFileList,\n    forEach,\n    merge,\n    extend,\n    trim,\n    stripBOM,\n    inherits,\n    toFlatObject,\n    kindOf,\n    kindOfTest,\n    endsWith,\n    toArray,\n    forEachEntry,\n    matchAll,\n    isHTMLForm,\n    hasOwnProperty,\n    hasOwnProp: hasOwnProperty,\n    reduceDescriptors,\n    freezeMethods,\n    toObjectSet,\n    toCamelCase,\n    noop,\n    toFiniteNumber,\n    findKey,\n    global: _global,\n    isContextDefined,\n    ALPHABET,\n    generateString,\n    isSpecCompliantForm,\n    toJSONObject,\n    isAsyncFn,\n    isThenable,\n    setImmediate: _setImmediate,\n    asap\n};\n/**\n * Create an Error with the specified message, config, error code, request and response.\n *\n * @param {string} message The error message.\n * @param {string} [code] The error code (for example, 'ECONNABORTED').\n * @param {Object} [config] The config.\n * @param {Object} [request] The request.\n * @param {Object} [response] The response.\n *\n * @returns {Error} The created error.\n */ function AxiosError(message, code, config, request, response) {\n    Error.call(this);\n    if (Error.captureStackTrace) {\n        Error.captureStackTrace(this, this.constructor);\n    } else {\n        this.stack = new Error().stack;\n    }\n    this.message = message;\n    this.name = \"AxiosError\";\n    code && (this.code = code);\n    config && (this.config = config);\n    request && (this.request = request);\n    if (response) {\n        this.response = response;\n        this.status = response.status ? response.status : null;\n    }\n}\nutils$1.inherits(AxiosError, Error, {\n    toJSON: function toJSON() {\n        return {\n            // Standard\n            message: this.message,\n            name: this.name,\n            // Microsoft\n            description: this.description,\n            number: this.number,\n            // Mozilla\n            fileName: this.fileName,\n            lineNumber: this.lineNumber,\n            columnNumber: this.columnNumber,\n            stack: this.stack,\n            // Axios\n            config: utils$1.toJSONObject(this.config),\n            code: this.code,\n            status: this.status\n        };\n    }\n});\nconst prototype$1 = AxiosError.prototype;\nconst descriptors = {};\n[\n    \"ERR_BAD_OPTION_VALUE\",\n    \"ERR_BAD_OPTION\",\n    \"ECONNABORTED\",\n    \"ETIMEDOUT\",\n    \"ERR_NETWORK\",\n    \"ERR_FR_TOO_MANY_REDIRECTS\",\n    \"ERR_DEPRECATED\",\n    \"ERR_BAD_RESPONSE\",\n    \"ERR_BAD_REQUEST\",\n    \"ERR_CANCELED\",\n    \"ERR_NOT_SUPPORT\",\n    \"ERR_INVALID_URL\"\n].forEach((code)=>{\n    descriptors[code] = {\n        value: code\n    };\n});\nObject.defineProperties(AxiosError, descriptors);\nObject.defineProperty(prototype$1, \"isAxiosError\", {\n    value: true\n});\n// eslint-disable-next-line func-names\nAxiosError.from = (error, code, config, request, response, customProps)=>{\n    const axiosError = Object.create(prototype$1);\n    utils$1.toFlatObject(error, axiosError, function filter(obj) {\n        return obj !== Error.prototype;\n    }, (prop)=>{\n        return prop !== \"isAxiosError\";\n    });\n    AxiosError.call(axiosError, error.message, code, config, request, response);\n    axiosError.cause = error;\n    axiosError.name = error.name;\n    customProps && Object.assign(axiosError, customProps);\n    return axiosError;\n};\n/**\n * Determines if the given thing is a array or js object.\n *\n * @param {string} thing - The object or array to be visited.\n *\n * @returns {boolean}\n */ function isVisitable(thing) {\n    return utils$1.isPlainObject(thing) || utils$1.isArray(thing);\n}\n/**\n * It removes the brackets from the end of a string\n *\n * @param {string} key - The key of the parameter.\n *\n * @returns {string} the key without the brackets.\n */ function removeBrackets(key) {\n    return utils$1.endsWith(key, \"[]\") ? key.slice(0, -2) : key;\n}\n/**\n * It takes a path, a key, and a boolean, and returns a string\n *\n * @param {string} path - The path to the current key.\n * @param {string} key - The key of the current object being iterated over.\n * @param {string} dots - If true, the key will be rendered with dots instead of brackets.\n *\n * @returns {string} The path to the current key.\n */ function renderKey(path, key, dots) {\n    if (!path) return key;\n    return path.concat(key).map(function each(token, i) {\n        // eslint-disable-next-line no-param-reassign\n        token = removeBrackets(token);\n        return !dots && i ? \"[\" + token + \"]\" : token;\n    }).join(dots ? \".\" : \"\");\n}\n/**\n * If the array is an array and none of its elements are visitable, then it's a flat array.\n *\n * @param {Array<any>} arr - The array to check\n *\n * @returns {boolean}\n */ function isFlatArray(arr) {\n    return utils$1.isArray(arr) && !arr.some(isVisitable);\n}\nconst predicates = utils$1.toFlatObject(utils$1, {}, null, function filter(prop) {\n    return /^is[A-Z]/.test(prop);\n});\n/**\n * Convert a data object to FormData\n *\n * @param {Object} obj\n * @param {?Object} [formData]\n * @param {?Object} [options]\n * @param {Function} [options.visitor]\n * @param {Boolean} [options.metaTokens = true]\n * @param {Boolean} [options.dots = false]\n * @param {?Boolean} [options.indexes = false]\n *\n * @returns {Object}\n **/ /**\n * It converts an object into a FormData object\n *\n * @param {Object<any, any>} obj - The object to convert to form data.\n * @param {string} formData - The FormData object to append to.\n * @param {Object<string, any>} options\n *\n * @returns\n */ function toFormData(obj, formData, options) {\n    if (!utils$1.isObject(obj)) {\n        throw new TypeError(\"target must be an object\");\n    }\n    // eslint-disable-next-line no-param-reassign\n    formData = formData || new (FormData__default[\"default\"] || FormData)();\n    // eslint-disable-next-line no-param-reassign\n    options = utils$1.toFlatObject(options, {\n        metaTokens: true,\n        dots: false,\n        indexes: false\n    }, false, function defined(option, source) {\n        // eslint-disable-next-line no-eq-null,eqeqeq\n        return !utils$1.isUndefined(source[option]);\n    });\n    const metaTokens = options.metaTokens;\n    // eslint-disable-next-line no-use-before-define\n    const visitor = options.visitor || defaultVisitor;\n    const dots = options.dots;\n    const indexes = options.indexes;\n    const _Blob = options.Blob || typeof Blob !== \"undefined\" && Blob;\n    const useBlob = _Blob && utils$1.isSpecCompliantForm(formData);\n    if (!utils$1.isFunction(visitor)) {\n        throw new TypeError(\"visitor must be a function\");\n    }\n    function convertValue(value) {\n        if (value === null) return \"\";\n        if (utils$1.isDate(value)) {\n            return value.toISOString();\n        }\n        if (!useBlob && utils$1.isBlob(value)) {\n            throw new AxiosError(\"Blob is not supported. Use a Buffer instead.\");\n        }\n        if (utils$1.isArrayBuffer(value) || utils$1.isTypedArray(value)) {\n            return useBlob && typeof Blob === \"function\" ? new Blob([\n                value\n            ]) : Buffer.from(value);\n        }\n        return value;\n    }\n    /**\n   * Default visitor.\n   *\n   * @param {*} value\n   * @param {String|Number} key\n   * @param {Array<String|Number>} path\n   * @this {FormData}\n   *\n   * @returns {boolean} return true to visit the each prop of the value recursively\n   */ function defaultVisitor(value, key, path) {\n        let arr = value;\n        if (value && !path && typeof value === \"object\") {\n            if (utils$1.endsWith(key, \"{}\")) {\n                // eslint-disable-next-line no-param-reassign\n                key = metaTokens ? key : key.slice(0, -2);\n                // eslint-disable-next-line no-param-reassign\n                value = JSON.stringify(value);\n            } else if (utils$1.isArray(value) && isFlatArray(value) || (utils$1.isFileList(value) || utils$1.endsWith(key, \"[]\")) && (arr = utils$1.toArray(value))) {\n                // eslint-disable-next-line no-param-reassign\n                key = removeBrackets(key);\n                arr.forEach(function each(el, index) {\n                    !(utils$1.isUndefined(el) || el === null) && formData.append(// eslint-disable-next-line no-nested-ternary\n                    indexes === true ? renderKey([\n                        key\n                    ], index, dots) : indexes === null ? key : key + \"[]\", convertValue(el));\n                });\n                return false;\n            }\n        }\n        if (isVisitable(value)) {\n            return true;\n        }\n        formData.append(renderKey(path, key, dots), convertValue(value));\n        return false;\n    }\n    const stack = [];\n    const exposedHelpers = Object.assign(predicates, {\n        defaultVisitor,\n        convertValue,\n        isVisitable\n    });\n    function build(value, path) {\n        if (utils$1.isUndefined(value)) return;\n        if (stack.indexOf(value) !== -1) {\n            throw Error(\"Circular reference detected in \" + path.join(\".\"));\n        }\n        stack.push(value);\n        utils$1.forEach(value, function each(el, key) {\n            const result = !(utils$1.isUndefined(el) || el === null) && visitor.call(formData, el, utils$1.isString(key) ? key.trim() : key, path, exposedHelpers);\n            if (result === true) {\n                build(el, path ? path.concat(key) : [\n                    key\n                ]);\n            }\n        });\n        stack.pop();\n    }\n    if (!utils$1.isObject(obj)) {\n        throw new TypeError(\"data must be an object\");\n    }\n    build(obj);\n    return formData;\n}\n/**\n * It encodes a string by replacing all characters that are not in the unreserved set with\n * their percent-encoded equivalents\n *\n * @param {string} str - The string to encode.\n *\n * @returns {string} The encoded string.\n */ function encode$1(str) {\n    const charMap = {\n        \"!\": \"%21\",\n        \"'\": \"%27\",\n        \"(\": \"%28\",\n        \")\": \"%29\",\n        \"~\": \"%7E\",\n        \"%20\": \"+\",\n        \"%00\": \"\\x00\"\n    };\n    return encodeURIComponent(str).replace(/[!'()~]|%20|%00/g, function replacer(match) {\n        return charMap[match];\n    });\n}\n/**\n * It takes a params object and converts it to a FormData object\n *\n * @param {Object<string, any>} params - The parameters to be converted to a FormData object.\n * @param {Object<string, any>} options - The options object passed to the Axios constructor.\n *\n * @returns {void}\n */ function AxiosURLSearchParams(params, options) {\n    this._pairs = [];\n    params && toFormData(params, this, options);\n}\nconst prototype = AxiosURLSearchParams.prototype;\nprototype.append = function append(name, value) {\n    this._pairs.push([\n        name,\n        value\n    ]);\n};\nprototype.toString = function toString(encoder) {\n    const _encode = encoder ? function(value) {\n        return encoder.call(this, value, encode$1);\n    } : encode$1;\n    return this._pairs.map(function each(pair) {\n        return _encode(pair[0]) + \"=\" + _encode(pair[1]);\n    }, \"\").join(\"&\");\n};\n/**\n * It replaces all instances of the characters `:`, `$`, `,`, `+`, `[`, and `]` with their\n * URI encoded counterparts\n *\n * @param {string} val The value to be encoded.\n *\n * @returns {string} The encoded value.\n */ function encode(val) {\n    return encodeURIComponent(val).replace(/%3A/gi, \":\").replace(/%24/g, \"$\").replace(/%2C/gi, \",\").replace(/%20/g, \"+\").replace(/%5B/gi, \"[\").replace(/%5D/gi, \"]\");\n}\n/**\n * Build a URL by appending params to the end\n *\n * @param {string} url The base of the url (e.g., http://www.google.com)\n * @param {object} [params] The params to be appended\n * @param {?object} options\n *\n * @returns {string} The formatted url\n */ function buildURL(url, params, options) {\n    /*eslint no-param-reassign:0*/ if (!params) {\n        return url;\n    }\n    const _encode = options && options.encode || encode;\n    const serializeFn = options && options.serialize;\n    let serializedParams;\n    if (serializeFn) {\n        serializedParams = serializeFn(params, options);\n    } else {\n        serializedParams = utils$1.isURLSearchParams(params) ? params.toString() : new AxiosURLSearchParams(params, options).toString(_encode);\n    }\n    if (serializedParams) {\n        const hashmarkIndex = url.indexOf(\"#\");\n        if (hashmarkIndex !== -1) {\n            url = url.slice(0, hashmarkIndex);\n        }\n        url += (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + serializedParams;\n    }\n    return url;\n}\nclass InterceptorManager {\n    constructor(){\n        this.handlers = [];\n    }\n    /**\n   * Add a new interceptor to the stack\n   *\n   * @param {Function} fulfilled The function to handle `then` for a `Promise`\n   * @param {Function} rejected The function to handle `reject` for a `Promise`\n   *\n   * @return {Number} An ID used to remove interceptor later\n   */ use(fulfilled, rejected, options) {\n        this.handlers.push({\n            fulfilled,\n            rejected,\n            synchronous: options ? options.synchronous : false,\n            runWhen: options ? options.runWhen : null\n        });\n        return this.handlers.length - 1;\n    }\n    /**\n   * Remove an interceptor from the stack\n   *\n   * @param {Number} id The ID that was returned by `use`\n   *\n   * @returns {Boolean} `true` if the interceptor was removed, `false` otherwise\n   */ eject(id) {\n        if (this.handlers[id]) {\n            this.handlers[id] = null;\n        }\n    }\n    /**\n   * Clear all interceptors from the stack\n   *\n   * @returns {void}\n   */ clear() {\n        if (this.handlers) {\n            this.handlers = [];\n        }\n    }\n    /**\n   * Iterate over all the registered interceptors\n   *\n   * This method is particularly useful for skipping over any\n   * interceptors that may have become `null` calling `eject`.\n   *\n   * @param {Function} fn The function to call for each interceptor\n   *\n   * @returns {void}\n   */ forEach(fn) {\n        utils$1.forEach(this.handlers, function forEachHandler(h) {\n            if (h !== null) {\n                fn(h);\n            }\n        });\n    }\n}\nconst InterceptorManager$1 = InterceptorManager;\nconst transitionalDefaults = {\n    silentJSONParsing: true,\n    forcedJSONParsing: true,\n    clarifyTimeoutError: false\n};\nconst URLSearchParams = url__default[\"default\"].URLSearchParams;\nconst platform$1 = {\n    isNode: true,\n    classes: {\n        URLSearchParams,\n        FormData: FormData__default[\"default\"],\n        Blob: typeof Blob !== \"undefined\" && Blob || null\n    },\n    protocols: [\n        \"http\",\n        \"https\",\n        \"file\",\n        \"data\"\n    ]\n};\nconst hasBrowserEnv =  false && 0;\nconst _navigator = typeof navigator === \"object\" && navigator || undefined;\n/**\n * Determine if we're running in a standard browser environment\n *\n * This allows axios to run in a web worker, and react-native.\n * Both environments support XMLHttpRequest, but not fully standard globals.\n *\n * web workers:\n *  typeof window -> undefined\n *  typeof document -> undefined\n *\n * react-native:\n *  navigator.product -> 'ReactNative'\n * nativescript\n *  navigator.product -> 'NativeScript' or 'NS'\n *\n * @returns {boolean}\n */ const hasStandardBrowserEnv = hasBrowserEnv && (!_navigator || [\n    \"ReactNative\",\n    \"NativeScript\",\n    \"NS\"\n].indexOf(_navigator.product) < 0);\n/**\n * Determine if we're running in a standard browser webWorker environment\n *\n * Although the `isStandardBrowserEnv` method indicates that\n * `allows axios to run in a web worker`, the WebWorker will still be\n * filtered out due to its judgment standard\n * `typeof window !== 'undefined' && typeof document !== 'undefined'`.\n * This leads to a problem when axios post `FormData` in webWorker\n */ const hasStandardBrowserWebWorkerEnv = (()=>{\n    return typeof WorkerGlobalScope !== \"undefined\" && // eslint-disable-next-line no-undef\n    self instanceof WorkerGlobalScope && typeof self.importScripts === \"function\";\n})();\nconst origin = hasBrowserEnv && window.location.href || \"http://localhost\";\nconst utils = /*#__PURE__*/ Object.freeze({\n    __proto__: null,\n    hasBrowserEnv: hasBrowserEnv,\n    hasStandardBrowserWebWorkerEnv: hasStandardBrowserWebWorkerEnv,\n    hasStandardBrowserEnv: hasStandardBrowserEnv,\n    navigator: _navigator,\n    origin: origin\n});\nconst platform = {\n    ...utils,\n    ...platform$1\n};\nfunction toURLEncodedForm(data, options) {\n    return toFormData(data, new platform.classes.URLSearchParams(), Object.assign({\n        visitor: function(value, key, path, helpers) {\n            if (platform.isNode && utils$1.isBuffer(value)) {\n                this.append(key, value.toString(\"base64\"));\n                return false;\n            }\n            return helpers.defaultVisitor.apply(this, arguments);\n        }\n    }, options));\n}\n/**\n * It takes a string like `foo[x][y][z]` and returns an array like `['foo', 'x', 'y', 'z']\n *\n * @param {string} name - The name of the property to get.\n *\n * @returns An array of strings.\n */ function parsePropPath(name) {\n    // foo[x][y][z]\n    // foo.x.y.z\n    // foo-x-y-z\n    // foo x y z\n    return utils$1.matchAll(/\\w+|\\[(\\w*)]/g, name).map((match)=>{\n        return match[0] === \"[]\" ? \"\" : match[1] || match[0];\n    });\n}\n/**\n * Convert an array to an object.\n *\n * @param {Array<any>} arr - The array to convert to an object.\n *\n * @returns An object with the same keys and values as the array.\n */ function arrayToObject(arr) {\n    const obj = {};\n    const keys = Object.keys(arr);\n    let i;\n    const len = keys.length;\n    let key;\n    for(i = 0; i < len; i++){\n        key = keys[i];\n        obj[key] = arr[key];\n    }\n    return obj;\n}\n/**\n * It takes a FormData object and returns a JavaScript object\n *\n * @param {string} formData The FormData object to convert to JSON.\n *\n * @returns {Object<string, any> | null} The converted object.\n */ function formDataToJSON(formData) {\n    function buildPath(path, value, target, index) {\n        let name = path[index++];\n        if (name === \"__proto__\") return true;\n        const isNumericKey = Number.isFinite(+name);\n        const isLast = index >= path.length;\n        name = !name && utils$1.isArray(target) ? target.length : name;\n        if (isLast) {\n            if (utils$1.hasOwnProp(target, name)) {\n                target[name] = [\n                    target[name],\n                    value\n                ];\n            } else {\n                target[name] = value;\n            }\n            return !isNumericKey;\n        }\n        if (!target[name] || !utils$1.isObject(target[name])) {\n            target[name] = [];\n        }\n        const result = buildPath(path, value, target[name], index);\n        if (result && utils$1.isArray(target[name])) {\n            target[name] = arrayToObject(target[name]);\n        }\n        return !isNumericKey;\n    }\n    if (utils$1.isFormData(formData) && utils$1.isFunction(formData.entries)) {\n        const obj = {};\n        utils$1.forEachEntry(formData, (name, value)=>{\n            buildPath(parsePropPath(name), value, obj, 0);\n        });\n        return obj;\n    }\n    return null;\n}\n/**\n * It takes a string, tries to parse it, and if it fails, it returns the stringified version\n * of the input\n *\n * @param {any} rawValue - The value to be stringified.\n * @param {Function} parser - A function that parses a string into a JavaScript object.\n * @param {Function} encoder - A function that takes a value and returns a string.\n *\n * @returns {string} A stringified version of the rawValue.\n */ function stringifySafely(rawValue, parser, encoder) {\n    if (utils$1.isString(rawValue)) {\n        try {\n            (parser || JSON.parse)(rawValue);\n            return utils$1.trim(rawValue);\n        } catch (e) {\n            if (e.name !== \"SyntaxError\") {\n                throw e;\n            }\n        }\n    }\n    return (encoder || JSON.stringify)(rawValue);\n}\nconst defaults = {\n    transitional: transitionalDefaults,\n    adapter: [\n        \"xhr\",\n        \"http\",\n        \"fetch\"\n    ],\n    transformRequest: [\n        function transformRequest(data, headers) {\n            const contentType = headers.getContentType() || \"\";\n            const hasJSONContentType = contentType.indexOf(\"application/json\") > -1;\n            const isObjectPayload = utils$1.isObject(data);\n            if (isObjectPayload && utils$1.isHTMLForm(data)) {\n                data = new FormData(data);\n            }\n            const isFormData = utils$1.isFormData(data);\n            if (isFormData) {\n                return hasJSONContentType ? JSON.stringify(formDataToJSON(data)) : data;\n            }\n            if (utils$1.isArrayBuffer(data) || utils$1.isBuffer(data) || utils$1.isStream(data) || utils$1.isFile(data) || utils$1.isBlob(data) || utils$1.isReadableStream(data)) {\n                return data;\n            }\n            if (utils$1.isArrayBufferView(data)) {\n                return data.buffer;\n            }\n            if (utils$1.isURLSearchParams(data)) {\n                headers.setContentType(\"application/x-www-form-urlencoded;charset=utf-8\", false);\n                return data.toString();\n            }\n            let isFileList;\n            if (isObjectPayload) {\n                if (contentType.indexOf(\"application/x-www-form-urlencoded\") > -1) {\n                    return toURLEncodedForm(data, this.formSerializer).toString();\n                }\n                if ((isFileList = utils$1.isFileList(data)) || contentType.indexOf(\"multipart/form-data\") > -1) {\n                    const _FormData = this.env && this.env.FormData;\n                    return toFormData(isFileList ? {\n                        \"files[]\": data\n                    } : data, _FormData && new _FormData(), this.formSerializer);\n                }\n            }\n            if (isObjectPayload || hasJSONContentType) {\n                headers.setContentType(\"application/json\", false);\n                return stringifySafely(data);\n            }\n            return data;\n        }\n    ],\n    transformResponse: [\n        function transformResponse(data) {\n            const transitional = this.transitional || defaults.transitional;\n            const forcedJSONParsing = transitional && transitional.forcedJSONParsing;\n            const JSONRequested = this.responseType === \"json\";\n            if (utils$1.isResponse(data) || utils$1.isReadableStream(data)) {\n                return data;\n            }\n            if (data && utils$1.isString(data) && (forcedJSONParsing && !this.responseType || JSONRequested)) {\n                const silentJSONParsing = transitional && transitional.silentJSONParsing;\n                const strictJSONParsing = !silentJSONParsing && JSONRequested;\n                try {\n                    return JSON.parse(data);\n                } catch (e) {\n                    if (strictJSONParsing) {\n                        if (e.name === \"SyntaxError\") {\n                            throw AxiosError.from(e, AxiosError.ERR_BAD_RESPONSE, this, null, this.response);\n                        }\n                        throw e;\n                    }\n                }\n            }\n            return data;\n        }\n    ],\n    /**\n   * A timeout in milliseconds to abort a request. If set to 0 (default) a\n   * timeout is not created.\n   */ timeout: 0,\n    xsrfCookieName: \"XSRF-TOKEN\",\n    xsrfHeaderName: \"X-XSRF-TOKEN\",\n    maxContentLength: -1,\n    maxBodyLength: -1,\n    env: {\n        FormData: platform.classes.FormData,\n        Blob: platform.classes.Blob\n    },\n    validateStatus: function validateStatus(status) {\n        return status >= 200 && status < 300;\n    },\n    headers: {\n        common: {\n            \"Accept\": \"application/json, text/plain, */*\",\n            \"Content-Type\": undefined\n        }\n    }\n};\nutils$1.forEach([\n    \"delete\",\n    \"get\",\n    \"head\",\n    \"post\",\n    \"put\",\n    \"patch\"\n], (method)=>{\n    defaults.headers[method] = {};\n});\nconst defaults$1 = defaults;\n// RawAxiosHeaders whose duplicates are ignored by node\n// c.f. https://nodejs.org/api/http.html#http_message_headers\nconst ignoreDuplicateOf = utils$1.toObjectSet([\n    \"age\",\n    \"authorization\",\n    \"content-length\",\n    \"content-type\",\n    \"etag\",\n    \"expires\",\n    \"from\",\n    \"host\",\n    \"if-modified-since\",\n    \"if-unmodified-since\",\n    \"last-modified\",\n    \"location\",\n    \"max-forwards\",\n    \"proxy-authorization\",\n    \"referer\",\n    \"retry-after\",\n    \"user-agent\"\n]);\n/**\n * Parse headers into an object\n *\n * ```\n * Date: Wed, 27 Aug 2014 08:58:49 GMT\n * Content-Type: application/json\n * Connection: keep-alive\n * Transfer-Encoding: chunked\n * ```\n *\n * @param {String} rawHeaders Headers needing to be parsed\n *\n * @returns {Object} Headers parsed into an object\n */ const parseHeaders = (rawHeaders)=>{\n    const parsed = {};\n    let key;\n    let val;\n    let i;\n    rawHeaders && rawHeaders.split(\"\\n\").forEach(function parser(line) {\n        i = line.indexOf(\":\");\n        key = line.substring(0, i).trim().toLowerCase();\n        val = line.substring(i + 1).trim();\n        if (!key || parsed[key] && ignoreDuplicateOf[key]) {\n            return;\n        }\n        if (key === \"set-cookie\") {\n            if (parsed[key]) {\n                parsed[key].push(val);\n            } else {\n                parsed[key] = [\n                    val\n                ];\n            }\n        } else {\n            parsed[key] = parsed[key] ? parsed[key] + \", \" + val : val;\n        }\n    });\n    return parsed;\n};\nconst $internals = Symbol(\"internals\");\nfunction normalizeHeader(header) {\n    return header && String(header).trim().toLowerCase();\n}\nfunction normalizeValue(value) {\n    if (value === false || value == null) {\n        return value;\n    }\n    return utils$1.isArray(value) ? value.map(normalizeValue) : String(value);\n}\nfunction parseTokens(str) {\n    const tokens = Object.create(null);\n    const tokensRE = /([^\\s,;=]+)\\s*(?:=\\s*([^,;]+))?/g;\n    let match;\n    while(match = tokensRE.exec(str)){\n        tokens[match[1]] = match[2];\n    }\n    return tokens;\n}\nconst isValidHeaderName = (str)=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(str.trim());\nfunction matchHeaderValue(context, value, header, filter, isHeaderNameFilter) {\n    if (utils$1.isFunction(filter)) {\n        return filter.call(this, value, header);\n    }\n    if (isHeaderNameFilter) {\n        value = header;\n    }\n    if (!utils$1.isString(value)) return;\n    if (utils$1.isString(filter)) {\n        return value.indexOf(filter) !== -1;\n    }\n    if (utils$1.isRegExp(filter)) {\n        return filter.test(value);\n    }\n}\nfunction formatHeader(header) {\n    return header.trim().toLowerCase().replace(/([a-z\\d])(\\w*)/g, (w, char, str)=>{\n        return char.toUpperCase() + str;\n    });\n}\nfunction buildAccessors(obj, header) {\n    const accessorName = utils$1.toCamelCase(\" \" + header);\n    [\n        \"get\",\n        \"set\",\n        \"has\"\n    ].forEach((methodName)=>{\n        Object.defineProperty(obj, methodName + accessorName, {\n            value: function(arg1, arg2, arg3) {\n                return this[methodName].call(this, header, arg1, arg2, arg3);\n            },\n            configurable: true\n        });\n    });\n}\nclass AxiosHeaders {\n    constructor(headers){\n        headers && this.set(headers);\n    }\n    set(header, valueOrRewrite, rewrite) {\n        const self1 = this;\n        function setHeader(_value, _header, _rewrite) {\n            const lHeader = normalizeHeader(_header);\n            if (!lHeader) {\n                throw new Error(\"header name must be a non-empty string\");\n            }\n            const key = utils$1.findKey(self1, lHeader);\n            if (!key || self1[key] === undefined || _rewrite === true || _rewrite === undefined && self1[key] !== false) {\n                self1[key || _header] = normalizeValue(_value);\n            }\n        }\n        const setHeaders = (headers, _rewrite)=>utils$1.forEach(headers, (_value, _header)=>setHeader(_value, _header, _rewrite));\n        if (utils$1.isPlainObject(header) || header instanceof this.constructor) {\n            setHeaders(header, valueOrRewrite);\n        } else if (utils$1.isString(header) && (header = header.trim()) && !isValidHeaderName(header)) {\n            setHeaders(parseHeaders(header), valueOrRewrite);\n        } else if (utils$1.isHeaders(header)) {\n            for (const [key, value] of header.entries()){\n                setHeader(value, key, rewrite);\n            }\n        } else {\n            header != null && setHeader(valueOrRewrite, header, rewrite);\n        }\n        return this;\n    }\n    get(header, parser) {\n        header = normalizeHeader(header);\n        if (header) {\n            const key = utils$1.findKey(this, header);\n            if (key) {\n                const value = this[key];\n                if (!parser) {\n                    return value;\n                }\n                if (parser === true) {\n                    return parseTokens(value);\n                }\n                if (utils$1.isFunction(parser)) {\n                    return parser.call(this, value, key);\n                }\n                if (utils$1.isRegExp(parser)) {\n                    return parser.exec(value);\n                }\n                throw new TypeError(\"parser must be boolean|regexp|function\");\n            }\n        }\n    }\n    has(header, matcher) {\n        header = normalizeHeader(header);\n        if (header) {\n            const key = utils$1.findKey(this, header);\n            return !!(key && this[key] !== undefined && (!matcher || matchHeaderValue(this, this[key], key, matcher)));\n        }\n        return false;\n    }\n    delete(header, matcher) {\n        const self1 = this;\n        let deleted = false;\n        function deleteHeader(_header) {\n            _header = normalizeHeader(_header);\n            if (_header) {\n                const key = utils$1.findKey(self1, _header);\n                if (key && (!matcher || matchHeaderValue(self1, self1[key], key, matcher))) {\n                    delete self1[key];\n                    deleted = true;\n                }\n            }\n        }\n        if (utils$1.isArray(header)) {\n            header.forEach(deleteHeader);\n        } else {\n            deleteHeader(header);\n        }\n        return deleted;\n    }\n    clear(matcher) {\n        const keys = Object.keys(this);\n        let i = keys.length;\n        let deleted = false;\n        while(i--){\n            const key = keys[i];\n            if (!matcher || matchHeaderValue(this, this[key], key, matcher, true)) {\n                delete this[key];\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    normalize(format) {\n        const self1 = this;\n        const headers = {};\n        utils$1.forEach(this, (value, header)=>{\n            const key = utils$1.findKey(headers, header);\n            if (key) {\n                self1[key] = normalizeValue(value);\n                delete self1[header];\n                return;\n            }\n            const normalized = format ? formatHeader(header) : String(header).trim();\n            if (normalized !== header) {\n                delete self1[header];\n            }\n            self1[normalized] = normalizeValue(value);\n            headers[normalized] = true;\n        });\n        return this;\n    }\n    concat(...targets) {\n        return this.constructor.concat(this, ...targets);\n    }\n    toJSON(asStrings) {\n        const obj = Object.create(null);\n        utils$1.forEach(this, (value, header)=>{\n            value != null && value !== false && (obj[header] = asStrings && utils$1.isArray(value) ? value.join(\", \") : value);\n        });\n        return obj;\n    }\n    [Symbol.iterator]() {\n        return Object.entries(this.toJSON())[Symbol.iterator]();\n    }\n    toString() {\n        return Object.entries(this.toJSON()).map(([header, value])=>header + \": \" + value).join(\"\\n\");\n    }\n    get [Symbol.toStringTag]() {\n        return \"AxiosHeaders\";\n    }\n    static from(thing) {\n        return thing instanceof this ? thing : new this(thing);\n    }\n    static concat(first, ...targets) {\n        const computed = new this(first);\n        targets.forEach((target)=>computed.set(target));\n        return computed;\n    }\n    static accessor(header) {\n        const internals = this[$internals] = this[$internals] = {\n            accessors: {}\n        };\n        const accessors = internals.accessors;\n        const prototype = this.prototype;\n        function defineAccessor(_header) {\n            const lHeader = normalizeHeader(_header);\n            if (!accessors[lHeader]) {\n                buildAccessors(prototype, _header);\n                accessors[lHeader] = true;\n            }\n        }\n        utils$1.isArray(header) ? header.forEach(defineAccessor) : defineAccessor(header);\n        return this;\n    }\n}\nAxiosHeaders.accessor([\n    \"Content-Type\",\n    \"Content-Length\",\n    \"Accept\",\n    \"Accept-Encoding\",\n    \"User-Agent\",\n    \"Authorization\"\n]);\n// reserved names hotfix\nutils$1.reduceDescriptors(AxiosHeaders.prototype, ({ value }, key)=>{\n    let mapped = key[0].toUpperCase() + key.slice(1); // map `set` => `Set`\n    return {\n        get: ()=>value,\n        set (headerValue) {\n            this[mapped] = headerValue;\n        }\n    };\n});\nutils$1.freezeMethods(AxiosHeaders);\nconst AxiosHeaders$1 = AxiosHeaders;\n/**\n * Transform the data for a request or a response\n *\n * @param {Array|Function} fns A single function or Array of functions\n * @param {?Object} response The response object\n *\n * @returns {*} The resulting transformed data\n */ function transformData(fns, response) {\n    const config = this || defaults$1;\n    const context = response || config;\n    const headers = AxiosHeaders$1.from(context.headers);\n    let data = context.data;\n    utils$1.forEach(fns, function transform(fn) {\n        data = fn.call(config, data, headers.normalize(), response ? response.status : undefined);\n    });\n    headers.normalize();\n    return data;\n}\nfunction isCancel(value) {\n    return !!(value && value.__CANCEL__);\n}\n/**\n * A `CanceledError` is an object that is thrown when an operation is canceled.\n *\n * @param {string=} message The message.\n * @param {Object=} config The config.\n * @param {Object=} request The request.\n *\n * @returns {CanceledError} The created error.\n */ function CanceledError(message, config, request) {\n    // eslint-disable-next-line no-eq-null,eqeqeq\n    AxiosError.call(this, message == null ? \"canceled\" : message, AxiosError.ERR_CANCELED, config, request);\n    this.name = \"CanceledError\";\n}\nutils$1.inherits(CanceledError, AxiosError, {\n    __CANCEL__: true\n});\n/**\n * Resolve or reject a Promise based on response status.\n *\n * @param {Function} resolve A function that resolves the promise.\n * @param {Function} reject A function that rejects the promise.\n * @param {object} response The response.\n *\n * @returns {object} The response.\n */ function settle(resolve, reject, response) {\n    const validateStatus = response.config.validateStatus;\n    if (!response.status || !validateStatus || validateStatus(response.status)) {\n        resolve(response);\n    } else {\n        reject(new AxiosError(\"Request failed with status code \" + response.status, [\n            AxiosError.ERR_BAD_REQUEST,\n            AxiosError.ERR_BAD_RESPONSE\n        ][Math.floor(response.status / 100) - 4], response.config, response.request, response));\n    }\n}\n/**\n * Determines whether the specified URL is absolute\n *\n * @param {string} url The URL to test\n *\n * @returns {boolean} True if the specified URL is absolute, otherwise false\n */ function isAbsoluteURL(url) {\n    // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\n    // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\n    // by any combination of letters, digits, plus, period, or hyphen.\n    return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url);\n}\n/**\n * Creates a new URL by combining the specified URLs\n *\n * @param {string} baseURL The base URL\n * @param {string} relativeURL The relative URL\n *\n * @returns {string} The combined URL\n */ function combineURLs(baseURL, relativeURL) {\n    return relativeURL ? baseURL.replace(/\\/?\\/$/, \"\") + \"/\" + relativeURL.replace(/^\\/+/, \"\") : baseURL;\n}\n/**\n * Creates a new URL by combining the baseURL with the requestedURL,\n * only when the requestedURL is not already an absolute URL.\n * If the requestURL is absolute, this function returns the requestedURL untouched.\n *\n * @param {string} baseURL The base URL\n * @param {string} requestedURL Absolute or relative URL to combine\n *\n * @returns {string} The combined full path\n */ function buildFullPath(baseURL, requestedURL) {\n    if (baseURL && !isAbsoluteURL(requestedURL)) {\n        return combineURLs(baseURL, requestedURL);\n    }\n    return requestedURL;\n}\nconst VERSION = \"1.7.7\";\nfunction parseProtocol(url) {\n    const match = /^([-+\\w]{1,25})(:?\\/\\/|:)/.exec(url);\n    return match && match[1] || \"\";\n}\nconst DATA_URL_PATTERN = /^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\\s\\S]*)$/;\n/**\n * Parse data uri to a Buffer or Blob\n *\n * @param {String} uri\n * @param {?Boolean} asBlob\n * @param {?Object} options\n * @param {?Function} options.Blob\n *\n * @returns {Buffer|Blob}\n */ function fromDataURI(uri, asBlob, options) {\n    const _Blob = options && options.Blob || platform.classes.Blob;\n    const protocol = parseProtocol(uri);\n    if (asBlob === undefined && _Blob) {\n        asBlob = true;\n    }\n    if (protocol === \"data\") {\n        uri = protocol.length ? uri.slice(protocol.length + 1) : uri;\n        const match = DATA_URL_PATTERN.exec(uri);\n        if (!match) {\n            throw new AxiosError(\"Invalid URL\", AxiosError.ERR_INVALID_URL);\n        }\n        const mime = match[1];\n        const isBase64 = match[2];\n        const body = match[3];\n        const buffer = Buffer.from(decodeURIComponent(body), isBase64 ? \"base64\" : \"utf8\");\n        if (asBlob) {\n            if (!_Blob) {\n                throw new AxiosError(\"Blob is not supported\", AxiosError.ERR_NOT_SUPPORT);\n            }\n            return new _Blob([\n                buffer\n            ], {\n                type: mime\n            });\n        }\n        return buffer;\n    }\n    throw new AxiosError(\"Unsupported protocol \" + protocol, AxiosError.ERR_NOT_SUPPORT);\n}\nconst kInternals = Symbol(\"internals\");\nclass AxiosTransformStream extends stream__default[\"default\"].Transform {\n    constructor(options){\n        options = utils$1.toFlatObject(options, {\n            maxRate: 0,\n            chunkSize: 64 * 1024,\n            minChunkSize: 100,\n            timeWindow: 500,\n            ticksRate: 2,\n            samplesCount: 15\n        }, null, (prop, source)=>{\n            return !utils$1.isUndefined(source[prop]);\n        });\n        super({\n            readableHighWaterMark: options.chunkSize\n        });\n        const internals = this[kInternals] = {\n            timeWindow: options.timeWindow,\n            chunkSize: options.chunkSize,\n            maxRate: options.maxRate,\n            minChunkSize: options.minChunkSize,\n            bytesSeen: 0,\n            isCaptured: false,\n            notifiedBytesLoaded: 0,\n            ts: Date.now(),\n            bytes: 0,\n            onReadCallback: null\n        };\n        this.on(\"newListener\", (event)=>{\n            if (event === \"progress\") {\n                if (!internals.isCaptured) {\n                    internals.isCaptured = true;\n                }\n            }\n        });\n    }\n    _read(size) {\n        const internals = this[kInternals];\n        if (internals.onReadCallback) {\n            internals.onReadCallback();\n        }\n        return super._read(size);\n    }\n    _transform(chunk, encoding, callback) {\n        const internals = this[kInternals];\n        const maxRate = internals.maxRate;\n        const readableHighWaterMark = this.readableHighWaterMark;\n        const timeWindow = internals.timeWindow;\n        const divider = 1000 / timeWindow;\n        const bytesThreshold = maxRate / divider;\n        const minChunkSize = internals.minChunkSize !== false ? Math.max(internals.minChunkSize, bytesThreshold * 0.01) : 0;\n        const pushChunk = (_chunk, _callback)=>{\n            const bytes = Buffer.byteLength(_chunk);\n            internals.bytesSeen += bytes;\n            internals.bytes += bytes;\n            internals.isCaptured && this.emit(\"progress\", internals.bytesSeen);\n            if (this.push(_chunk)) {\n                process.nextTick(_callback);\n            } else {\n                internals.onReadCallback = ()=>{\n                    internals.onReadCallback = null;\n                    process.nextTick(_callback);\n                };\n            }\n        };\n        const transformChunk = (_chunk, _callback)=>{\n            const chunkSize = Buffer.byteLength(_chunk);\n            let chunkRemainder = null;\n            let maxChunkSize = readableHighWaterMark;\n            let bytesLeft;\n            let passed = 0;\n            if (maxRate) {\n                const now = Date.now();\n                if (!internals.ts || (passed = now - internals.ts) >= timeWindow) {\n                    internals.ts = now;\n                    bytesLeft = bytesThreshold - internals.bytes;\n                    internals.bytes = bytesLeft < 0 ? -bytesLeft : 0;\n                    passed = 0;\n                }\n                bytesLeft = bytesThreshold - internals.bytes;\n            }\n            if (maxRate) {\n                if (bytesLeft <= 0) {\n                    // next time window\n                    return setTimeout(()=>{\n                        _callback(null, _chunk);\n                    }, timeWindow - passed);\n                }\n                if (bytesLeft < maxChunkSize) {\n                    maxChunkSize = bytesLeft;\n                }\n            }\n            if (maxChunkSize && chunkSize > maxChunkSize && chunkSize - maxChunkSize > minChunkSize) {\n                chunkRemainder = _chunk.subarray(maxChunkSize);\n                _chunk = _chunk.subarray(0, maxChunkSize);\n            }\n            pushChunk(_chunk, chunkRemainder ? ()=>{\n                process.nextTick(_callback, null, chunkRemainder);\n            } : _callback);\n        };\n        transformChunk(chunk, function transformNextChunk(err, _chunk) {\n            if (err) {\n                return callback(err);\n            }\n            if (_chunk) {\n                transformChunk(_chunk, transformNextChunk);\n            } else {\n                callback(null);\n            }\n        });\n    }\n}\nconst AxiosTransformStream$1 = AxiosTransformStream;\nconst { asyncIterator } = Symbol;\nconst readBlob = async function*(blob) {\n    if (blob.stream) {\n        yield* blob.stream();\n    } else if (blob.arrayBuffer) {\n        yield await blob.arrayBuffer();\n    } else if (blob[asyncIterator]) {\n        yield* blob[asyncIterator]();\n    } else {\n        yield blob;\n    }\n};\nconst readBlob$1 = readBlob;\nconst BOUNDARY_ALPHABET = utils$1.ALPHABET.ALPHA_DIGIT + \"-_\";\nconst textEncoder = new util.TextEncoder();\nconst CRLF = \"\\r\\n\";\nconst CRLF_BYTES = textEncoder.encode(CRLF);\nconst CRLF_BYTES_COUNT = 2;\nclass FormDataPart {\n    constructor(name, value){\n        const { escapeName } = this.constructor;\n        const isStringValue = utils$1.isString(value);\n        let headers = `Content-Disposition: form-data; name=\"${escapeName(name)}\"${!isStringValue && value.name ? `; filename=\"${escapeName(value.name)}\"` : \"\"}${CRLF}`;\n        if (isStringValue) {\n            value = textEncoder.encode(String(value).replace(/\\r?\\n|\\r\\n?/g, CRLF));\n        } else {\n            headers += `Content-Type: ${value.type || \"application/octet-stream\"}${CRLF}`;\n        }\n        this.headers = textEncoder.encode(headers + CRLF);\n        this.contentLength = isStringValue ? value.byteLength : value.size;\n        this.size = this.headers.byteLength + this.contentLength + CRLF_BYTES_COUNT;\n        this.name = name;\n        this.value = value;\n    }\n    async *encode() {\n        yield this.headers;\n        const { value } = this;\n        if (utils$1.isTypedArray(value)) {\n            yield value;\n        } else {\n            yield* readBlob$1(value);\n        }\n        yield CRLF_BYTES;\n    }\n    static escapeName(name) {\n        return String(name).replace(/[\\r\\n\"]/g, (match)=>({\n                \"\\r\": \"%0D\",\n                \"\\n\": \"%0A\",\n                '\"': \"%22\"\n            })[match]);\n    }\n}\nconst formDataToStream = (form, headersHandler, options)=>{\n    const { tag = \"form-data-boundary\", size = 25, boundary = tag + \"-\" + utils$1.generateString(size, BOUNDARY_ALPHABET) } = options || {};\n    if (!utils$1.isFormData(form)) {\n        throw TypeError(\"FormData instance required\");\n    }\n    if (boundary.length < 1 || boundary.length > 70) {\n        throw Error(\"boundary must be 10-70 characters long\");\n    }\n    const boundaryBytes = textEncoder.encode(\"--\" + boundary + CRLF);\n    const footerBytes = textEncoder.encode(\"--\" + boundary + \"--\" + CRLF + CRLF);\n    let contentLength = footerBytes.byteLength;\n    const parts = Array.from(form.entries()).map(([name, value])=>{\n        const part = new FormDataPart(name, value);\n        contentLength += part.size;\n        return part;\n    });\n    contentLength += boundaryBytes.byteLength * parts.length;\n    contentLength = utils$1.toFiniteNumber(contentLength);\n    const computedHeaders = {\n        \"Content-Type\": `multipart/form-data; boundary=${boundary}`\n    };\n    if (Number.isFinite(contentLength)) {\n        computedHeaders[\"Content-Length\"] = contentLength;\n    }\n    headersHandler && headersHandler(computedHeaders);\n    return stream.Readable.from(async function*() {\n        for (const part of parts){\n            yield boundaryBytes;\n            yield* part.encode();\n        }\n        yield footerBytes;\n    }());\n};\nconst formDataToStream$1 = formDataToStream;\nclass ZlibHeaderTransformStream extends stream__default[\"default\"].Transform {\n    __transform(chunk, encoding, callback) {\n        this.push(chunk);\n        callback();\n    }\n    _transform(chunk, encoding, callback) {\n        if (chunk.length !== 0) {\n            this._transform = this.__transform;\n            // Add Default Compression headers if no zlib headers are present\n            if (chunk[0] !== 120) {\n                const header = Buffer.alloc(2);\n                header[0] = 120; // Hex: 78\n                header[1] = 156; // Hex: 9C \n                this.push(header, encoding);\n            }\n        }\n        this.__transform(chunk, encoding, callback);\n    }\n}\nconst ZlibHeaderTransformStream$1 = ZlibHeaderTransformStream;\nconst callbackify = (fn, reducer)=>{\n    return utils$1.isAsyncFn(fn) ? function(...args) {\n        const cb = args.pop();\n        fn.apply(this, args).then((value)=>{\n            try {\n                reducer ? cb(null, ...reducer(value)) : cb(null, value);\n            } catch (err) {\n                cb(err);\n            }\n        }, cb);\n    } : fn;\n};\nconst callbackify$1 = callbackify;\n/**\n * Calculate data maxRate\n * @param {Number} [samplesCount= 10]\n * @param {Number} [min= 1000]\n * @returns {Function}\n */ function speedometer(samplesCount, min) {\n    samplesCount = samplesCount || 10;\n    const bytes = new Array(samplesCount);\n    const timestamps = new Array(samplesCount);\n    let head = 0;\n    let tail = 0;\n    let firstSampleTS;\n    min = min !== undefined ? min : 1000;\n    return function push(chunkLength) {\n        const now = Date.now();\n        const startedAt = timestamps[tail];\n        if (!firstSampleTS) {\n            firstSampleTS = now;\n        }\n        bytes[head] = chunkLength;\n        timestamps[head] = now;\n        let i = tail;\n        let bytesCount = 0;\n        while(i !== head){\n            bytesCount += bytes[i++];\n            i = i % samplesCount;\n        }\n        head = (head + 1) % samplesCount;\n        if (head === tail) {\n            tail = (tail + 1) % samplesCount;\n        }\n        if (now - firstSampleTS < min) {\n            return;\n        }\n        const passed = startedAt && now - startedAt;\n        return passed ? Math.round(bytesCount * 1000 / passed) : undefined;\n    };\n}\n/**\n * Throttle decorator\n * @param {Function} fn\n * @param {Number} freq\n * @return {Function}\n */ function throttle(fn, freq) {\n    let timestamp = 0;\n    let threshold = 1000 / freq;\n    let lastArgs;\n    let timer;\n    const invoke = (args, now = Date.now())=>{\n        timestamp = now;\n        lastArgs = null;\n        if (timer) {\n            clearTimeout(timer);\n            timer = null;\n        }\n        fn.apply(null, args);\n    };\n    const throttled = (...args)=>{\n        const now = Date.now();\n        const passed = now - timestamp;\n        if (passed >= threshold) {\n            invoke(args, now);\n        } else {\n            lastArgs = args;\n            if (!timer) {\n                timer = setTimeout(()=>{\n                    timer = null;\n                    invoke(lastArgs);\n                }, threshold - passed);\n            }\n        }\n    };\n    const flush = ()=>lastArgs && invoke(lastArgs);\n    return [\n        throttled,\n        flush\n    ];\n}\nconst progressEventReducer = (listener, isDownloadStream, freq = 3)=>{\n    let bytesNotified = 0;\n    const _speedometer = speedometer(50, 250);\n    return throttle((e)=>{\n        const loaded = e.loaded;\n        const total = e.lengthComputable ? e.total : undefined;\n        const progressBytes = loaded - bytesNotified;\n        const rate = _speedometer(progressBytes);\n        const inRange = loaded <= total;\n        bytesNotified = loaded;\n        const data = {\n            loaded,\n            total,\n            progress: total ? loaded / total : undefined,\n            bytes: progressBytes,\n            rate: rate ? rate : undefined,\n            estimated: rate && total && inRange ? (total - loaded) / rate : undefined,\n            event: e,\n            lengthComputable: total != null,\n            [isDownloadStream ? \"download\" : \"upload\"]: true\n        };\n        listener(data);\n    }, freq);\n};\nconst progressEventDecorator = (total, throttled)=>{\n    const lengthComputable = total != null;\n    return [\n        (loaded)=>throttled[0]({\n                lengthComputable,\n                total,\n                loaded\n            }),\n        throttled[1]\n    ];\n};\nconst asyncDecorator = (fn)=>(...args)=>utils$1.asap(()=>fn(...args));\nconst zlibOptions = {\n    flush: zlib__default[\"default\"].constants.Z_SYNC_FLUSH,\n    finishFlush: zlib__default[\"default\"].constants.Z_SYNC_FLUSH\n};\nconst brotliOptions = {\n    flush: zlib__default[\"default\"].constants.BROTLI_OPERATION_FLUSH,\n    finishFlush: zlib__default[\"default\"].constants.BROTLI_OPERATION_FLUSH\n};\nconst isBrotliSupported = utils$1.isFunction(zlib__default[\"default\"].createBrotliDecompress);\nconst { http: httpFollow, https: httpsFollow } = followRedirects__default[\"default\"];\nconst isHttps = /https:?/;\nconst supportedProtocols = platform.protocols.map((protocol)=>{\n    return protocol + \":\";\n});\nconst flushOnFinish = (stream, [throttled, flush])=>{\n    stream.on(\"end\", flush).on(\"error\", flush);\n    return throttled;\n};\n/**\n * If the proxy or config beforeRedirects functions are defined, call them with the options\n * object.\n *\n * @param {Object<string, any>} options - The options object that was passed to the request.\n *\n * @returns {Object<string, any>}\n */ function dispatchBeforeRedirect(options, responseDetails) {\n    if (options.beforeRedirects.proxy) {\n        options.beforeRedirects.proxy(options);\n    }\n    if (options.beforeRedirects.config) {\n        options.beforeRedirects.config(options, responseDetails);\n    }\n}\n/**\n * If the proxy or config afterRedirects functions are defined, call them with the options\n *\n * @param {http.ClientRequestArgs} options\n * @param {AxiosProxyConfig} configProxy configuration from Axios options object\n * @param {string} location\n *\n * @returns {http.ClientRequestArgs}\n */ function setProxy(options, configProxy, location) {\n    let proxy = configProxy;\n    if (!proxy && proxy !== false) {\n        const proxyUrl = proxyFromEnv.getProxyForUrl(location);\n        if (proxyUrl) {\n            proxy = new URL(proxyUrl);\n        }\n    }\n    if (proxy) {\n        // Basic proxy authorization\n        if (proxy.username) {\n            proxy.auth = (proxy.username || \"\") + \":\" + (proxy.password || \"\");\n        }\n        if (proxy.auth) {\n            // Support proxy auth object form\n            if (proxy.auth.username || proxy.auth.password) {\n                proxy.auth = (proxy.auth.username || \"\") + \":\" + (proxy.auth.password || \"\");\n            }\n            const base64 = Buffer.from(proxy.auth, \"utf8\").toString(\"base64\");\n            options.headers[\"Proxy-Authorization\"] = \"Basic \" + base64;\n        }\n        options.headers.host = options.hostname + (options.port ? \":\" + options.port : \"\");\n        const proxyHost = proxy.hostname || proxy.host;\n        options.hostname = proxyHost;\n        // Replace 'host' since options is not a URL object\n        options.host = proxyHost;\n        options.port = proxy.port;\n        options.path = location;\n        if (proxy.protocol) {\n            options.protocol = proxy.protocol.includes(\":\") ? proxy.protocol : `${proxy.protocol}:`;\n        }\n    }\n    options.beforeRedirects.proxy = function beforeRedirect(redirectOptions) {\n        // Configure proxy for redirected request, passing the original config proxy to apply\n        // the exact same logic as if the redirected request was performed by axios directly.\n        setProxy(redirectOptions, configProxy, redirectOptions.href);\n    };\n}\nconst isHttpAdapterSupported = typeof process !== \"undefined\" && utils$1.kindOf(process) === \"process\";\n// temporary hotfix\nconst wrapAsync = (asyncExecutor)=>{\n    return new Promise((resolve, reject)=>{\n        let onDone;\n        let isDone;\n        const done = (value, isRejected)=>{\n            if (isDone) return;\n            isDone = true;\n            onDone && onDone(value, isRejected);\n        };\n        const _resolve = (value)=>{\n            done(value);\n            resolve(value);\n        };\n        const _reject = (reason)=>{\n            done(reason, true);\n            reject(reason);\n        };\n        asyncExecutor(_resolve, _reject, (onDoneHandler)=>onDone = onDoneHandler).catch(_reject);\n    });\n};\nconst resolveFamily = ({ address, family })=>{\n    if (!utils$1.isString(address)) {\n        throw TypeError(\"address must be a string\");\n    }\n    return {\n        address,\n        family: family || (address.indexOf(\".\") < 0 ? 6 : 4)\n    };\n};\nconst buildAddressEntry = (address, family)=>resolveFamily(utils$1.isObject(address) ? address : {\n        address,\n        family\n    });\n/*eslint consistent-return:0*/ const httpAdapter = isHttpAdapterSupported && function httpAdapter(config) {\n    return wrapAsync(async function dispatchHttpRequest(resolve, reject, onDone) {\n        let { data, lookup, family } = config;\n        const { responseType, responseEncoding } = config;\n        const method = config.method.toUpperCase();\n        let isDone;\n        let rejected = false;\n        let req;\n        if (lookup) {\n            const _lookup = callbackify$1(lookup, (value)=>utils$1.isArray(value) ? value : [\n                    value\n                ]);\n            // hotfix to support opt.all option which is required for node 20.x\n            lookup = (hostname, opt, cb)=>{\n                _lookup(hostname, opt, (err, arg0, arg1)=>{\n                    if (err) {\n                        return cb(err);\n                    }\n                    const addresses = utils$1.isArray(arg0) ? arg0.map((addr)=>buildAddressEntry(addr)) : [\n                        buildAddressEntry(arg0, arg1)\n                    ];\n                    opt.all ? cb(err, addresses) : cb(err, addresses[0].address, addresses[0].family);\n                });\n            };\n        }\n        // temporary internal emitter until the AxiosRequest class will be implemented\n        const emitter = new events.EventEmitter();\n        const onFinished = ()=>{\n            if (config.cancelToken) {\n                config.cancelToken.unsubscribe(abort);\n            }\n            if (config.signal) {\n                config.signal.removeEventListener(\"abort\", abort);\n            }\n            emitter.removeAllListeners();\n        };\n        onDone((value, isRejected)=>{\n            isDone = true;\n            if (isRejected) {\n                rejected = true;\n                onFinished();\n            }\n        });\n        function abort(reason) {\n            emitter.emit(\"abort\", !reason || reason.type ? new CanceledError(null, config, req) : reason);\n        }\n        emitter.once(\"abort\", reject);\n        if (config.cancelToken || config.signal) {\n            config.cancelToken && config.cancelToken.subscribe(abort);\n            if (config.signal) {\n                config.signal.aborted ? abort() : config.signal.addEventListener(\"abort\", abort);\n            }\n        }\n        // Parse url\n        const fullPath = buildFullPath(config.baseURL, config.url);\n        const parsed = new URL(fullPath, platform.hasBrowserEnv ? platform.origin : undefined);\n        const protocol = parsed.protocol || supportedProtocols[0];\n        if (protocol === \"data:\") {\n            let convertedData;\n            if (method !== \"GET\") {\n                return settle(resolve, reject, {\n                    status: 405,\n                    statusText: \"method not allowed\",\n                    headers: {},\n                    config\n                });\n            }\n            try {\n                convertedData = fromDataURI(config.url, responseType === \"blob\", {\n                    Blob: config.env && config.env.Blob\n                });\n            } catch (err) {\n                throw AxiosError.from(err, AxiosError.ERR_BAD_REQUEST, config);\n            }\n            if (responseType === \"text\") {\n                convertedData = convertedData.toString(responseEncoding);\n                if (!responseEncoding || responseEncoding === \"utf8\") {\n                    convertedData = utils$1.stripBOM(convertedData);\n                }\n            } else if (responseType === \"stream\") {\n                convertedData = stream__default[\"default\"].Readable.from(convertedData);\n            }\n            return settle(resolve, reject, {\n                data: convertedData,\n                status: 200,\n                statusText: \"OK\",\n                headers: new AxiosHeaders$1(),\n                config\n            });\n        }\n        if (supportedProtocols.indexOf(protocol) === -1) {\n            return reject(new AxiosError(\"Unsupported protocol \" + protocol, AxiosError.ERR_BAD_REQUEST, config));\n        }\n        const headers = AxiosHeaders$1.from(config.headers).normalize();\n        // Set User-Agent (required by some servers)\n        // See https://github.com/axios/axios/issues/69\n        // User-Agent is specified; handle case where no UA header is desired\n        // Only set header if it hasn't been set in config\n        headers.set(\"User-Agent\", \"axios/\" + VERSION, false);\n        const { onUploadProgress, onDownloadProgress } = config;\n        const maxRate = config.maxRate;\n        let maxUploadRate = undefined;\n        let maxDownloadRate = undefined;\n        // support for spec compliant FormData objects\n        if (utils$1.isSpecCompliantForm(data)) {\n            const userBoundary = headers.getContentType(/boundary=([-_\\w\\d]{10,70})/i);\n            data = formDataToStream$1(data, (formHeaders)=>{\n                headers.set(formHeaders);\n            }, {\n                tag: `axios-${VERSION}-boundary`,\n                boundary: userBoundary && userBoundary[1] || undefined\n            });\n        // support for https://www.npmjs.com/package/form-data api\n        } else if (utils$1.isFormData(data) && utils$1.isFunction(data.getHeaders)) {\n            headers.set(data.getHeaders());\n            if (!headers.hasContentLength()) {\n                try {\n                    const knownLength = await util__default[\"default\"].promisify(data.getLength).call(data);\n                    Number.isFinite(knownLength) && knownLength >= 0 && headers.setContentLength(knownLength);\n                /*eslint no-empty:0*/ } catch (e) {}\n            }\n        } else if (utils$1.isBlob(data)) {\n            data.size && headers.setContentType(data.type || \"application/octet-stream\");\n            headers.setContentLength(data.size || 0);\n            data = stream__default[\"default\"].Readable.from(readBlob$1(data));\n        } else if (data && !utils$1.isStream(data)) {\n            if (Buffer.isBuffer(data)) ;\n            else if (utils$1.isArrayBuffer(data)) {\n                data = Buffer.from(new Uint8Array(data));\n            } else if (utils$1.isString(data)) {\n                data = Buffer.from(data, \"utf-8\");\n            } else {\n                return reject(new AxiosError(\"Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream\", AxiosError.ERR_BAD_REQUEST, config));\n            }\n            // Add Content-Length header if data exists\n            headers.setContentLength(data.length, false);\n            if (config.maxBodyLength > -1 && data.length > config.maxBodyLength) {\n                return reject(new AxiosError(\"Request body larger than maxBodyLength limit\", AxiosError.ERR_BAD_REQUEST, config));\n            }\n        }\n        const contentLength = utils$1.toFiniteNumber(headers.getContentLength());\n        if (utils$1.isArray(maxRate)) {\n            maxUploadRate = maxRate[0];\n            maxDownloadRate = maxRate[1];\n        } else {\n            maxUploadRate = maxDownloadRate = maxRate;\n        }\n        if (data && (onUploadProgress || maxUploadRate)) {\n            if (!utils$1.isStream(data)) {\n                data = stream__default[\"default\"].Readable.from(data, {\n                    objectMode: false\n                });\n            }\n            data = stream__default[\"default\"].pipeline([\n                data,\n                new AxiosTransformStream$1({\n                    maxRate: utils$1.toFiniteNumber(maxUploadRate)\n                })\n            ], utils$1.noop);\n            onUploadProgress && data.on(\"progress\", flushOnFinish(data, progressEventDecorator(contentLength, progressEventReducer(asyncDecorator(onUploadProgress), false, 3))));\n        }\n        // HTTP basic authentication\n        let auth = undefined;\n        if (config.auth) {\n            const username = config.auth.username || \"\";\n            const password = config.auth.password || \"\";\n            auth = username + \":\" + password;\n        }\n        if (!auth && parsed.username) {\n            const urlUsername = parsed.username;\n            const urlPassword = parsed.password;\n            auth = urlUsername + \":\" + urlPassword;\n        }\n        auth && headers.delete(\"authorization\");\n        let path;\n        try {\n            path = buildURL(parsed.pathname + parsed.search, config.params, config.paramsSerializer).replace(/^\\?/, \"\");\n        } catch (err) {\n            const customErr = new Error(err.message);\n            customErr.config = config;\n            customErr.url = config.url;\n            customErr.exists = true;\n            return reject(customErr);\n        }\n        headers.set(\"Accept-Encoding\", \"gzip, compress, deflate\" + (isBrotliSupported ? \", br\" : \"\"), false);\n        const options = {\n            path,\n            method: method,\n            headers: headers.toJSON(),\n            agents: {\n                http: config.httpAgent,\n                https: config.httpsAgent\n            },\n            auth,\n            protocol,\n            family,\n            beforeRedirect: dispatchBeforeRedirect,\n            beforeRedirects: {}\n        };\n        // cacheable-lookup integration hotfix\n        !utils$1.isUndefined(lookup) && (options.lookup = lookup);\n        if (config.socketPath) {\n            options.socketPath = config.socketPath;\n        } else {\n            options.hostname = parsed.hostname.startsWith(\"[\") ? parsed.hostname.slice(1, -1) : parsed.hostname;\n            options.port = parsed.port;\n            setProxy(options, config.proxy, protocol + \"//\" + parsed.hostname + (parsed.port ? \":\" + parsed.port : \"\") + options.path);\n        }\n        let transport;\n        const isHttpsRequest = isHttps.test(options.protocol);\n        options.agent = isHttpsRequest ? config.httpsAgent : config.httpAgent;\n        if (config.transport) {\n            transport = config.transport;\n        } else if (config.maxRedirects === 0) {\n            transport = isHttpsRequest ? https__default[\"default\"] : http__default[\"default\"];\n        } else {\n            if (config.maxRedirects) {\n                options.maxRedirects = config.maxRedirects;\n            }\n            if (config.beforeRedirect) {\n                options.beforeRedirects.config = config.beforeRedirect;\n            }\n            transport = isHttpsRequest ? httpsFollow : httpFollow;\n        }\n        if (config.maxBodyLength > -1) {\n            options.maxBodyLength = config.maxBodyLength;\n        } else {\n            // follow-redirects does not skip comparison, so it should always succeed for axios -1 unlimited\n            options.maxBodyLength = Infinity;\n        }\n        if (config.insecureHTTPParser) {\n            options.insecureHTTPParser = config.insecureHTTPParser;\n        }\n        // Create the request\n        req = transport.request(options, function handleResponse(res) {\n            if (req.destroyed) return;\n            const streams = [\n                res\n            ];\n            const responseLength = +res.headers[\"content-length\"];\n            if (onDownloadProgress || maxDownloadRate) {\n                const transformStream = new AxiosTransformStream$1({\n                    maxRate: utils$1.toFiniteNumber(maxDownloadRate)\n                });\n                onDownloadProgress && transformStream.on(\"progress\", flushOnFinish(transformStream, progressEventDecorator(responseLength, progressEventReducer(asyncDecorator(onDownloadProgress), true, 3))));\n                streams.push(transformStream);\n            }\n            // decompress the response body transparently if required\n            let responseStream = res;\n            // return the last request in case of redirects\n            const lastRequest = res.req || req;\n            // if decompress disabled we should not decompress\n            if (config.decompress !== false && res.headers[\"content-encoding\"]) {\n                // if no content, but headers still say that it is encoded,\n                // remove the header not confuse downstream operations\n                if (method === \"HEAD\" || res.statusCode === 204) {\n                    delete res.headers[\"content-encoding\"];\n                }\n                switch((res.headers[\"content-encoding\"] || \"\").toLowerCase()){\n                    /*eslint default-case:0*/ case \"gzip\":\n                    case \"x-gzip\":\n                    case \"compress\":\n                    case \"x-compress\":\n                        // add the unzipper to the body stream processing pipeline\n                        streams.push(zlib__default[\"default\"].createUnzip(zlibOptions));\n                        // remove the content-encoding in order to not confuse downstream operations\n                        delete res.headers[\"content-encoding\"];\n                        break;\n                    case \"deflate\":\n                        streams.push(new ZlibHeaderTransformStream$1());\n                        // add the unzipper to the body stream processing pipeline\n                        streams.push(zlib__default[\"default\"].createUnzip(zlibOptions));\n                        // remove the content-encoding in order to not confuse downstream operations\n                        delete res.headers[\"content-encoding\"];\n                        break;\n                    case \"br\":\n                        if (isBrotliSupported) {\n                            streams.push(zlib__default[\"default\"].createBrotliDecompress(brotliOptions));\n                            delete res.headers[\"content-encoding\"];\n                        }\n                }\n            }\n            responseStream = streams.length > 1 ? stream__default[\"default\"].pipeline(streams, utils$1.noop) : streams[0];\n            const offListeners = stream__default[\"default\"].finished(responseStream, ()=>{\n                offListeners();\n                onFinished();\n            });\n            const response = {\n                status: res.statusCode,\n                statusText: res.statusMessage,\n                headers: new AxiosHeaders$1(res.headers),\n                config,\n                request: lastRequest\n            };\n            if (responseType === \"stream\") {\n                response.data = responseStream;\n                settle(resolve, reject, response);\n            } else {\n                const responseBuffer = [];\n                let totalResponseBytes = 0;\n                responseStream.on(\"data\", function handleStreamData(chunk) {\n                    responseBuffer.push(chunk);\n                    totalResponseBytes += chunk.length;\n                    // make sure the content length is not over the maxContentLength if specified\n                    if (config.maxContentLength > -1 && totalResponseBytes > config.maxContentLength) {\n                        // stream.destroy() emit aborted event before calling reject() on Node.js v16\n                        rejected = true;\n                        responseStream.destroy();\n                        reject(new AxiosError(\"maxContentLength size of \" + config.maxContentLength + \" exceeded\", AxiosError.ERR_BAD_RESPONSE, config, lastRequest));\n                    }\n                });\n                responseStream.on(\"aborted\", function handlerStreamAborted() {\n                    if (rejected) {\n                        return;\n                    }\n                    const err = new AxiosError(\"maxContentLength size of \" + config.maxContentLength + \" exceeded\", AxiosError.ERR_BAD_RESPONSE, config, lastRequest);\n                    responseStream.destroy(err);\n                    reject(err);\n                });\n                responseStream.on(\"error\", function handleStreamError(err) {\n                    if (req.destroyed) return;\n                    reject(AxiosError.from(err, null, config, lastRequest));\n                });\n                responseStream.on(\"end\", function handleStreamEnd() {\n                    try {\n                        let responseData = responseBuffer.length === 1 ? responseBuffer[0] : Buffer.concat(responseBuffer);\n                        if (responseType !== \"arraybuffer\") {\n                            responseData = responseData.toString(responseEncoding);\n                            if (!responseEncoding || responseEncoding === \"utf8\") {\n                                responseData = utils$1.stripBOM(responseData);\n                            }\n                        }\n                        response.data = responseData;\n                    } catch (err) {\n                        return reject(AxiosError.from(err, null, config, response.request, response));\n                    }\n                    settle(resolve, reject, response);\n                });\n            }\n            emitter.once(\"abort\", (err)=>{\n                if (!responseStream.destroyed) {\n                    responseStream.emit(\"error\", err);\n                    responseStream.destroy();\n                }\n            });\n        });\n        emitter.once(\"abort\", (err)=>{\n            reject(err);\n            req.destroy(err);\n        });\n        // Handle errors\n        req.on(\"error\", function handleRequestError(err) {\n            // @todo remove\n            // if (req.aborted && err.code !== AxiosError.ERR_FR_TOO_MANY_REDIRECTS) return;\n            reject(AxiosError.from(err, null, config, req));\n        });\n        // set tcp keep alive to prevent drop connection by peer\n        req.on(\"socket\", function handleRequestSocket(socket) {\n            // default interval of sending ack packet is 1 minute\n            socket.setKeepAlive(true, 1000 * 60);\n        });\n        // Handle request timeout\n        if (config.timeout) {\n            // This is forcing a int timeout to avoid problems if the `req` interface doesn't handle other types.\n            const timeout = parseInt(config.timeout, 10);\n            if (Number.isNaN(timeout)) {\n                reject(new AxiosError(\"error trying to parse `config.timeout` to int\", AxiosError.ERR_BAD_OPTION_VALUE, config, req));\n                return;\n            }\n            // Sometime, the response will be very slow, and does not respond, the connect event will be block by event loop system.\n            // And timer callback will be fired, and abort() will be invoked before connection, then get \"socket hang up\" and code ECONNRESET.\n            // At this time, if we have a large number of request, nodejs will hang up some socket on background. and the number will up and up.\n            // And then these socket which be hang up will devouring CPU little by little.\n            // ClientRequest.setTimeout will be fired on the specify milliseconds, and can make sure that abort() will be fired after connect.\n            req.setTimeout(timeout, function handleRequestTimeout() {\n                if (isDone) return;\n                let timeoutErrorMessage = config.timeout ? \"timeout of \" + config.timeout + \"ms exceeded\" : \"timeout exceeded\";\n                const transitional = config.transitional || transitionalDefaults;\n                if (config.timeoutErrorMessage) {\n                    timeoutErrorMessage = config.timeoutErrorMessage;\n                }\n                reject(new AxiosError(timeoutErrorMessage, transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED, config, req));\n                abort();\n            });\n        }\n        // Send the request\n        if (utils$1.isStream(data)) {\n            let ended = false;\n            let errored = false;\n            data.on(\"end\", ()=>{\n                ended = true;\n            });\n            data.once(\"error\", (err)=>{\n                errored = true;\n                req.destroy(err);\n            });\n            data.on(\"close\", ()=>{\n                if (!ended && !errored) {\n                    abort(new CanceledError(\"Request stream has been aborted\", config, req));\n                }\n            });\n            data.pipe(req);\n        } else {\n            req.end(data);\n        }\n    });\n};\nconst isURLSameOrigin = platform.hasStandardBrowserEnv ? // Standard browser envs have full support of the APIs needed to test\n// whether the request URL is of the same origin as current location.\nfunction standardBrowserEnv() {\n    const msie = platform.navigator && /(msie|trident)/i.test(platform.navigator.userAgent);\n    const urlParsingNode = document.createElement(\"a\");\n    let originURL;\n    /**\n    * Parse a URL to discover its components\n    *\n    * @param {String} url The URL to be parsed\n    * @returns {Object}\n    */ function resolveURL(url) {\n        let href = url;\n        if (msie) {\n            // IE needs attribute set twice to normalize properties\n            urlParsingNode.setAttribute(\"href\", href);\n            href = urlParsingNode.href;\n        }\n        urlParsingNode.setAttribute(\"href\", href);\n        // urlParsingNode provides the UrlUtils interface - http://url.spec.whatwg.org/#urlutils\n        return {\n            href: urlParsingNode.href,\n            protocol: urlParsingNode.protocol ? urlParsingNode.protocol.replace(/:$/, \"\") : \"\",\n            host: urlParsingNode.host,\n            search: urlParsingNode.search ? urlParsingNode.search.replace(/^\\?/, \"\") : \"\",\n            hash: urlParsingNode.hash ? urlParsingNode.hash.replace(/^#/, \"\") : \"\",\n            hostname: urlParsingNode.hostname,\n            port: urlParsingNode.port,\n            pathname: urlParsingNode.pathname.charAt(0) === \"/\" ? urlParsingNode.pathname : \"/\" + urlParsingNode.pathname\n        };\n    }\n    originURL = resolveURL(window.location.href);\n    /**\n    * Determine if a URL shares the same origin as the current location\n    *\n    * @param {String} requestURL The URL to test\n    * @returns {boolean} True if URL shares the same origin, otherwise false\n    */ return function isURLSameOrigin(requestURL) {\n        const parsed = utils$1.isString(requestURL) ? resolveURL(requestURL) : requestURL;\n        return parsed.protocol === originURL.protocol && parsed.host === originURL.host;\n    };\n}() : // Non standard browser envs (web workers, react-native) lack needed support.\nfunction nonStandardBrowserEnv() {\n    return function isURLSameOrigin() {\n        return true;\n    };\n}();\nconst cookies = platform.hasStandardBrowserEnv ? // Standard browser envs support document.cookie\n{\n    write (name, value, expires, path, domain, secure) {\n        const cookie = [\n            name + \"=\" + encodeURIComponent(value)\n        ];\n        utils$1.isNumber(expires) && cookie.push(\"expires=\" + new Date(expires).toGMTString());\n        utils$1.isString(path) && cookie.push(\"path=\" + path);\n        utils$1.isString(domain) && cookie.push(\"domain=\" + domain);\n        secure === true && cookie.push(\"secure\");\n        document.cookie = cookie.join(\"; \");\n    },\n    read (name) {\n        const match = document.cookie.match(new RegExp(\"(^|;\\\\s*)(\" + name + \")=([^;]*)\"));\n        return match ? decodeURIComponent(match[3]) : null;\n    },\n    remove (name) {\n        this.write(name, \"\", Date.now() - 86400000);\n    }\n} : // Non-standard browser env (web workers, react-native) lack needed support.\n{\n    write () {},\n    read () {\n        return null;\n    },\n    remove () {}\n};\nconst headersToObject = (thing)=>thing instanceof AxiosHeaders$1 ? {\n        ...thing\n    } : thing;\n/**\n * Config-specific merge-function which creates a new config-object\n * by merging two configuration objects together.\n *\n * @param {Object} config1\n * @param {Object} config2\n *\n * @returns {Object} New object resulting from merging config2 to config1\n */ function mergeConfig(config1, config2) {\n    // eslint-disable-next-line no-param-reassign\n    config2 = config2 || {};\n    const config = {};\n    function getMergedValue(target, source, caseless) {\n        if (utils$1.isPlainObject(target) && utils$1.isPlainObject(source)) {\n            return utils$1.merge.call({\n                caseless\n            }, target, source);\n        } else if (utils$1.isPlainObject(source)) {\n            return utils$1.merge({}, source);\n        } else if (utils$1.isArray(source)) {\n            return source.slice();\n        }\n        return source;\n    }\n    // eslint-disable-next-line consistent-return\n    function mergeDeepProperties(a, b, caseless) {\n        if (!utils$1.isUndefined(b)) {\n            return getMergedValue(a, b, caseless);\n        } else if (!utils$1.isUndefined(a)) {\n            return getMergedValue(undefined, a, caseless);\n        }\n    }\n    // eslint-disable-next-line consistent-return\n    function valueFromConfig2(a, b) {\n        if (!utils$1.isUndefined(b)) {\n            return getMergedValue(undefined, b);\n        }\n    }\n    // eslint-disable-next-line consistent-return\n    function defaultToConfig2(a, b) {\n        if (!utils$1.isUndefined(b)) {\n            return getMergedValue(undefined, b);\n        } else if (!utils$1.isUndefined(a)) {\n            return getMergedValue(undefined, a);\n        }\n    }\n    // eslint-disable-next-line consistent-return\n    function mergeDirectKeys(a, b, prop) {\n        if (prop in config2) {\n            return getMergedValue(a, b);\n        } else if (prop in config1) {\n            return getMergedValue(undefined, a);\n        }\n    }\n    const mergeMap = {\n        url: valueFromConfig2,\n        method: valueFromConfig2,\n        data: valueFromConfig2,\n        baseURL: defaultToConfig2,\n        transformRequest: defaultToConfig2,\n        transformResponse: defaultToConfig2,\n        paramsSerializer: defaultToConfig2,\n        timeout: defaultToConfig2,\n        timeoutMessage: defaultToConfig2,\n        withCredentials: defaultToConfig2,\n        withXSRFToken: defaultToConfig2,\n        adapter: defaultToConfig2,\n        responseType: defaultToConfig2,\n        xsrfCookieName: defaultToConfig2,\n        xsrfHeaderName: defaultToConfig2,\n        onUploadProgress: defaultToConfig2,\n        onDownloadProgress: defaultToConfig2,\n        decompress: defaultToConfig2,\n        maxContentLength: defaultToConfig2,\n        maxBodyLength: defaultToConfig2,\n        beforeRedirect: defaultToConfig2,\n        transport: defaultToConfig2,\n        httpAgent: defaultToConfig2,\n        httpsAgent: defaultToConfig2,\n        cancelToken: defaultToConfig2,\n        socketPath: defaultToConfig2,\n        responseEncoding: defaultToConfig2,\n        validateStatus: mergeDirectKeys,\n        headers: (a, b)=>mergeDeepProperties(headersToObject(a), headersToObject(b), true)\n    };\n    utils$1.forEach(Object.keys(Object.assign({}, config1, config2)), function computeConfigValue(prop) {\n        const merge = mergeMap[prop] || mergeDeepProperties;\n        const configValue = merge(config1[prop], config2[prop], prop);\n        utils$1.isUndefined(configValue) && merge !== mergeDirectKeys || (config[prop] = configValue);\n    });\n    return config;\n}\nconst resolveConfig = (config)=>{\n    const newConfig = mergeConfig({}, config);\n    let { data, withXSRFToken, xsrfHeaderName, xsrfCookieName, headers, auth } = newConfig;\n    newConfig.headers = headers = AxiosHeaders$1.from(headers);\n    newConfig.url = buildURL(buildFullPath(newConfig.baseURL, newConfig.url), config.params, config.paramsSerializer);\n    // HTTP basic authentication\n    if (auth) {\n        headers.set(\"Authorization\", \"Basic \" + btoa((auth.username || \"\") + \":\" + (auth.password ? unescape(encodeURIComponent(auth.password)) : \"\")));\n    }\n    let contentType;\n    if (utils$1.isFormData(data)) {\n        if (platform.hasStandardBrowserEnv || platform.hasStandardBrowserWebWorkerEnv) {\n            headers.setContentType(undefined); // Let the browser set it\n        } else if ((contentType = headers.getContentType()) !== false) {\n            // fix semicolon duplication issue for ReactNative FormData implementation\n            const [type, ...tokens] = contentType ? contentType.split(\";\").map((token)=>token.trim()).filter(Boolean) : [];\n            headers.setContentType([\n                type || \"multipart/form-data\",\n                ...tokens\n            ].join(\"; \"));\n        }\n    }\n    // Add xsrf header\n    // This is only done if running in a standard browser environment.\n    // Specifically not if we're in a web worker, or react-native.\n    if (platform.hasStandardBrowserEnv) {\n        withXSRFToken && utils$1.isFunction(withXSRFToken) && (withXSRFToken = withXSRFToken(newConfig));\n        if (withXSRFToken || withXSRFToken !== false && isURLSameOrigin(newConfig.url)) {\n            // Add xsrf header\n            const xsrfValue = xsrfHeaderName && xsrfCookieName && cookies.read(xsrfCookieName);\n            if (xsrfValue) {\n                headers.set(xsrfHeaderName, xsrfValue);\n            }\n        }\n    }\n    return newConfig;\n};\nconst isXHRAdapterSupported = typeof XMLHttpRequest !== \"undefined\";\nconst xhrAdapter = isXHRAdapterSupported && function(config) {\n    return new Promise(function dispatchXhrRequest(resolve, reject) {\n        const _config = resolveConfig(config);\n        let requestData = _config.data;\n        const requestHeaders = AxiosHeaders$1.from(_config.headers).normalize();\n        let { responseType, onUploadProgress, onDownloadProgress } = _config;\n        let onCanceled;\n        let uploadThrottled, downloadThrottled;\n        let flushUpload, flushDownload;\n        function done() {\n            flushUpload && flushUpload(); // flush events\n            flushDownload && flushDownload(); // flush events\n            _config.cancelToken && _config.cancelToken.unsubscribe(onCanceled);\n            _config.signal && _config.signal.removeEventListener(\"abort\", onCanceled);\n        }\n        let request = new XMLHttpRequest();\n        request.open(_config.method.toUpperCase(), _config.url, true);\n        // Set the request timeout in MS\n        request.timeout = _config.timeout;\n        function onloadend() {\n            if (!request) {\n                return;\n            }\n            // Prepare the response\n            const responseHeaders = AxiosHeaders$1.from(\"getAllResponseHeaders\" in request && request.getAllResponseHeaders());\n            const responseData = !responseType || responseType === \"text\" || responseType === \"json\" ? request.responseText : request.response;\n            const response = {\n                data: responseData,\n                status: request.status,\n                statusText: request.statusText,\n                headers: responseHeaders,\n                config,\n                request\n            };\n            settle(function _resolve(value) {\n                resolve(value);\n                done();\n            }, function _reject(err) {\n                reject(err);\n                done();\n            }, response);\n            // Clean up request\n            request = null;\n        }\n        if (\"onloadend\" in request) {\n            // Use onloadend if available\n            request.onloadend = onloadend;\n        } else {\n            // Listen for ready state to emulate onloadend\n            request.onreadystatechange = function handleLoad() {\n                if (!request || request.readyState !== 4) {\n                    return;\n                }\n                // The request errored out and we didn't get a response, this will be\n                // handled by onerror instead\n                // With one exception: request that using file: protocol, most browsers\n                // will return status as 0 even though it's a successful request\n                if (request.status === 0 && !(request.responseURL && request.responseURL.indexOf(\"file:\") === 0)) {\n                    return;\n                }\n                // readystate handler is calling before onerror or ontimeout handlers,\n                // so we should call onloadend on the next 'tick'\n                setTimeout(onloadend);\n            };\n        }\n        // Handle browser request cancellation (as opposed to a manual cancellation)\n        request.onabort = function handleAbort() {\n            if (!request) {\n                return;\n            }\n            reject(new AxiosError(\"Request aborted\", AxiosError.ECONNABORTED, config, request));\n            // Clean up request\n            request = null;\n        };\n        // Handle low level network errors\n        request.onerror = function handleError() {\n            // Real errors are hidden from us by the browser\n            // onerror should only fire if it's a network error\n            reject(new AxiosError(\"Network Error\", AxiosError.ERR_NETWORK, config, request));\n            // Clean up request\n            request = null;\n        };\n        // Handle timeout\n        request.ontimeout = function handleTimeout() {\n            let timeoutErrorMessage = _config.timeout ? \"timeout of \" + _config.timeout + \"ms exceeded\" : \"timeout exceeded\";\n            const transitional = _config.transitional || transitionalDefaults;\n            if (_config.timeoutErrorMessage) {\n                timeoutErrorMessage = _config.timeoutErrorMessage;\n            }\n            reject(new AxiosError(timeoutErrorMessage, transitional.clarifyTimeoutError ? AxiosError.ETIMEDOUT : AxiosError.ECONNABORTED, config, request));\n            // Clean up request\n            request = null;\n        };\n        // Remove Content-Type if data is undefined\n        requestData === undefined && requestHeaders.setContentType(null);\n        // Add headers to the request\n        if (\"setRequestHeader\" in request) {\n            utils$1.forEach(requestHeaders.toJSON(), function setRequestHeader(val, key) {\n                request.setRequestHeader(key, val);\n            });\n        }\n        // Add withCredentials to request if needed\n        if (!utils$1.isUndefined(_config.withCredentials)) {\n            request.withCredentials = !!_config.withCredentials;\n        }\n        // Add responseType to request if needed\n        if (responseType && responseType !== \"json\") {\n            request.responseType = _config.responseType;\n        }\n        // Handle progress if needed\n        if (onDownloadProgress) {\n            [downloadThrottled, flushDownload] = progressEventReducer(onDownloadProgress, true);\n            request.addEventListener(\"progress\", downloadThrottled);\n        }\n        // Not all browsers support upload events\n        if (onUploadProgress && request.upload) {\n            [uploadThrottled, flushUpload] = progressEventReducer(onUploadProgress);\n            request.upload.addEventListener(\"progress\", uploadThrottled);\n            request.upload.addEventListener(\"loadend\", flushUpload);\n        }\n        if (_config.cancelToken || _config.signal) {\n            // Handle cancellation\n            // eslint-disable-next-line func-names\n            onCanceled = (cancel)=>{\n                if (!request) {\n                    return;\n                }\n                reject(!cancel || cancel.type ? new CanceledError(null, config, request) : cancel);\n                request.abort();\n                request = null;\n            };\n            _config.cancelToken && _config.cancelToken.subscribe(onCanceled);\n            if (_config.signal) {\n                _config.signal.aborted ? onCanceled() : _config.signal.addEventListener(\"abort\", onCanceled);\n            }\n        }\n        const protocol = parseProtocol(_config.url);\n        if (protocol && platform.protocols.indexOf(protocol) === -1) {\n            reject(new AxiosError(\"Unsupported protocol \" + protocol + \":\", AxiosError.ERR_BAD_REQUEST, config));\n            return;\n        }\n        // Send the request\n        request.send(requestData || null);\n    });\n};\nconst composeSignals = (signals, timeout)=>{\n    const { length } = signals = signals ? signals.filter(Boolean) : [];\n    if (timeout || length) {\n        let controller = new AbortController();\n        let aborted;\n        const onabort = function(reason) {\n            if (!aborted) {\n                aborted = true;\n                unsubscribe();\n                const err = reason instanceof Error ? reason : this.reason;\n                controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n            }\n        };\n        let timer = timeout && setTimeout(()=>{\n            timer = null;\n            onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));\n        }, timeout);\n        const unsubscribe = ()=>{\n            if (signals) {\n                timer && clearTimeout(timer);\n                timer = null;\n                signals.forEach((signal)=>{\n                    signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener(\"abort\", onabort);\n                });\n                signals = null;\n            }\n        };\n        signals.forEach((signal)=>signal.addEventListener(\"abort\", onabort));\n        const { signal } = controller;\n        signal.unsubscribe = ()=>utils$1.asap(unsubscribe);\n        return signal;\n    }\n};\nconst composeSignals$1 = composeSignals;\nconst streamChunk = function*(chunk, chunkSize) {\n    let len = chunk.byteLength;\n    if (!chunkSize || len < chunkSize) {\n        yield chunk;\n        return;\n    }\n    let pos = 0;\n    let end;\n    while(pos < len){\n        end = pos + chunkSize;\n        yield chunk.slice(pos, end);\n        pos = end;\n    }\n};\nconst readBytes = async function*(iterable, chunkSize) {\n    for await (const chunk of readStream(iterable)){\n        yield* streamChunk(chunk, chunkSize);\n    }\n};\nconst readStream = async function*(stream) {\n    if (stream[Symbol.asyncIterator]) {\n        yield* stream;\n        return;\n    }\n    const reader = stream.getReader();\n    try {\n        for(;;){\n            const { done, value } = await reader.read();\n            if (done) {\n                break;\n            }\n            yield value;\n        }\n    } finally{\n        await reader.cancel();\n    }\n};\nconst trackStream = (stream, chunkSize, onProgress, onFinish)=>{\n    const iterator = readBytes(stream, chunkSize);\n    let bytes = 0;\n    let done;\n    let _onFinish = (e)=>{\n        if (!done) {\n            done = true;\n            onFinish && onFinish(e);\n        }\n    };\n    return new ReadableStream({\n        async pull (controller) {\n            try {\n                const { done, value } = await iterator.next();\n                if (done) {\n                    _onFinish();\n                    controller.close();\n                    return;\n                }\n                let len = value.byteLength;\n                if (onProgress) {\n                    let loadedBytes = bytes += len;\n                    onProgress(loadedBytes);\n                }\n                controller.enqueue(new Uint8Array(value));\n            } catch (err) {\n                _onFinish(err);\n                throw err;\n            }\n        },\n        cancel (reason) {\n            _onFinish(reason);\n            return iterator.return();\n        }\n    }, {\n        highWaterMark: 2\n    });\n};\nconst isFetchSupported = typeof fetch === \"function\" && typeof Request === \"function\" && typeof Response === \"function\";\nconst isReadableStreamSupported = isFetchSupported && typeof ReadableStream === \"function\";\n// used only inside the fetch adapter\nconst encodeText = isFetchSupported && (typeof TextEncoder === \"function\" ? ((encoder)=>(str)=>encoder.encode(str))(new TextEncoder()) : async (str)=>new Uint8Array(await new Response(str).arrayBuffer()));\nconst test = (fn, ...args)=>{\n    try {\n        return !!fn(...args);\n    } catch (e) {\n        return false;\n    }\n};\nconst supportsRequestStream = isReadableStreamSupported && test(()=>{\n    let duplexAccessed = false;\n    const hasContentType = new Request(platform.origin, {\n        body: new ReadableStream(),\n        method: \"POST\",\n        get duplex () {\n            duplexAccessed = true;\n            return \"half\";\n        }\n    }).headers.has(\"Content-Type\");\n    return duplexAccessed && !hasContentType;\n});\nconst DEFAULT_CHUNK_SIZE = 64 * 1024;\nconst supportsResponseStream = isReadableStreamSupported && test(()=>utils$1.isReadableStream(new Response(\"\").body));\nconst resolvers = {\n    stream: supportsResponseStream && ((res)=>res.body)\n};\nisFetchSupported && ((res)=>{\n    [\n        \"text\",\n        \"arrayBuffer\",\n        \"blob\",\n        \"formData\",\n        \"stream\"\n    ].forEach((type)=>{\n        !resolvers[type] && (resolvers[type] = utils$1.isFunction(res[type]) ? (res)=>res[type]() : (_, config)=>{\n            throw new AxiosError(`Response type '${type}' is not supported`, AxiosError.ERR_NOT_SUPPORT, config);\n        });\n    });\n})(new Response);\nconst getBodyLength = async (body)=>{\n    if (body == null) {\n        return 0;\n    }\n    if (utils$1.isBlob(body)) {\n        return body.size;\n    }\n    if (utils$1.isSpecCompliantForm(body)) {\n        const _request = new Request(platform.origin, {\n            method: \"POST\",\n            body\n        });\n        return (await _request.arrayBuffer()).byteLength;\n    }\n    if (utils$1.isArrayBufferView(body) || utils$1.isArrayBuffer(body)) {\n        return body.byteLength;\n    }\n    if (utils$1.isURLSearchParams(body)) {\n        body = body + \"\";\n    }\n    if (utils$1.isString(body)) {\n        return (await encodeText(body)).byteLength;\n    }\n};\nconst resolveBodyLength = async (headers, body)=>{\n    const length = utils$1.toFiniteNumber(headers.getContentLength());\n    return length == null ? getBodyLength(body) : length;\n};\nconst fetchAdapter = isFetchSupported && (async (config)=>{\n    let { url, method, data, signal, cancelToken, timeout, onDownloadProgress, onUploadProgress, responseType, headers, withCredentials = \"same-origin\", fetchOptions } = resolveConfig(config);\n    responseType = responseType ? (responseType + \"\").toLowerCase() : \"text\";\n    let composedSignal = composeSignals$1([\n        signal,\n        cancelToken && cancelToken.toAbortSignal()\n    ], timeout);\n    let request;\n    const unsubscribe = composedSignal && composedSignal.unsubscribe && (()=>{\n        composedSignal.unsubscribe();\n    });\n    let requestContentLength;\n    try {\n        if (onUploadProgress && supportsRequestStream && method !== \"get\" && method !== \"head\" && (requestContentLength = await resolveBodyLength(headers, data)) !== 0) {\n            let _request = new Request(url, {\n                method: \"POST\",\n                body: data,\n                duplex: \"half\"\n            });\n            let contentTypeHeader;\n            if (utils$1.isFormData(data) && (contentTypeHeader = _request.headers.get(\"content-type\"))) {\n                headers.setContentType(contentTypeHeader);\n            }\n            if (_request.body) {\n                const [onProgress, flush] = progressEventDecorator(requestContentLength, progressEventReducer(asyncDecorator(onUploadProgress)));\n                data = trackStream(_request.body, DEFAULT_CHUNK_SIZE, onProgress, flush);\n            }\n        }\n        if (!utils$1.isString(withCredentials)) {\n            withCredentials = withCredentials ? \"include\" : \"omit\";\n        }\n        // Cloudflare Workers throws when credentials are defined\n        // see https://github.com/cloudflare/workerd/issues/902\n        const isCredentialsSupported = \"credentials\" in Request.prototype;\n        request = new Request(url, {\n            ...fetchOptions,\n            signal: composedSignal,\n            method: method.toUpperCase(),\n            headers: headers.normalize().toJSON(),\n            body: data,\n            duplex: \"half\",\n            credentials: isCredentialsSupported ? withCredentials : undefined\n        });\n        let response = await fetch(request);\n        const isStreamResponse = supportsResponseStream && (responseType === \"stream\" || responseType === \"response\");\n        if (supportsResponseStream && (onDownloadProgress || isStreamResponse && unsubscribe)) {\n            const options = {};\n            [\n                \"status\",\n                \"statusText\",\n                \"headers\"\n            ].forEach((prop)=>{\n                options[prop] = response[prop];\n            });\n            const responseContentLength = utils$1.toFiniteNumber(response.headers.get(\"content-length\"));\n            const [onProgress, flush] = onDownloadProgress && progressEventDecorator(responseContentLength, progressEventReducer(asyncDecorator(onDownloadProgress), true)) || [];\n            response = new Response(trackStream(response.body, DEFAULT_CHUNK_SIZE, onProgress, ()=>{\n                flush && flush();\n                unsubscribe && unsubscribe();\n            }), options);\n        }\n        responseType = responseType || \"text\";\n        let responseData = await resolvers[utils$1.findKey(resolvers, responseType) || \"text\"](response, config);\n        !isStreamResponse && unsubscribe && unsubscribe();\n        return await new Promise((resolve, reject)=>{\n            settle(resolve, reject, {\n                data: responseData,\n                headers: AxiosHeaders$1.from(response.headers),\n                status: response.status,\n                statusText: response.statusText,\n                config,\n                request\n            });\n        });\n    } catch (err) {\n        unsubscribe && unsubscribe();\n        if (err && err.name === \"TypeError\" && /fetch/i.test(err.message)) {\n            throw Object.assign(new AxiosError(\"Network Error\", AxiosError.ERR_NETWORK, config, request), {\n                cause: err.cause || err\n            });\n        }\n        throw AxiosError.from(err, err && err.code, config, request);\n    }\n});\nconst knownAdapters = {\n    http: httpAdapter,\n    xhr: xhrAdapter,\n    fetch: fetchAdapter\n};\nutils$1.forEach(knownAdapters, (fn, value)=>{\n    if (fn) {\n        try {\n            Object.defineProperty(fn, \"name\", {\n                value\n            });\n        } catch (e) {\n        // eslint-disable-next-line no-empty\n        }\n        Object.defineProperty(fn, \"adapterName\", {\n            value\n        });\n    }\n});\nconst renderReason = (reason)=>`- ${reason}`;\nconst isResolvedHandle = (adapter)=>utils$1.isFunction(adapter) || adapter === null || adapter === false;\nconst adapters = {\n    getAdapter: (adapters)=>{\n        adapters = utils$1.isArray(adapters) ? adapters : [\n            adapters\n        ];\n        const { length } = adapters;\n        let nameOrAdapter;\n        let adapter;\n        const rejectedReasons = {};\n        for(let i = 0; i < length; i++){\n            nameOrAdapter = adapters[i];\n            let id;\n            adapter = nameOrAdapter;\n            if (!isResolvedHandle(nameOrAdapter)) {\n                adapter = knownAdapters[(id = String(nameOrAdapter)).toLowerCase()];\n                if (adapter === undefined) {\n                    throw new AxiosError(`Unknown adapter '${id}'`);\n                }\n            }\n            if (adapter) {\n                break;\n            }\n            rejectedReasons[id || \"#\" + i] = adapter;\n        }\n        if (!adapter) {\n            const reasons = Object.entries(rejectedReasons).map(([id, state])=>`adapter ${id} ` + (state === false ? \"is not supported by the environment\" : \"is not available in the build\"));\n            let s = length ? reasons.length > 1 ? \"since :\\n\" + reasons.map(renderReason).join(\"\\n\") : \" \" + renderReason(reasons[0]) : \"as no adapter specified\";\n            throw new AxiosError(`There is no suitable adapter to dispatch the request ` + s, \"ERR_NOT_SUPPORT\");\n        }\n        return adapter;\n    },\n    adapters: knownAdapters\n};\n/**\n * Throws a `CanceledError` if cancellation has been requested.\n *\n * @param {Object} config The config that is to be used for the request\n *\n * @returns {void}\n */ function throwIfCancellationRequested(config) {\n    if (config.cancelToken) {\n        config.cancelToken.throwIfRequested();\n    }\n    if (config.signal && config.signal.aborted) {\n        throw new CanceledError(null, config);\n    }\n}\n/**\n * Dispatch a request to the server using the configured adapter.\n *\n * @param {object} config The config that is to be used for the request\n *\n * @returns {Promise} The Promise to be fulfilled\n */ function dispatchRequest(config) {\n    throwIfCancellationRequested(config);\n    config.headers = AxiosHeaders$1.from(config.headers);\n    // Transform request data\n    config.data = transformData.call(config, config.transformRequest);\n    if ([\n        \"post\",\n        \"put\",\n        \"patch\"\n    ].indexOf(config.method) !== -1) {\n        config.headers.setContentType(\"application/x-www-form-urlencoded\", false);\n    }\n    const adapter = adapters.getAdapter(config.adapter || defaults$1.adapter);\n    return adapter(config).then(function onAdapterResolution(response) {\n        throwIfCancellationRequested(config);\n        // Transform response data\n        response.data = transformData.call(config, config.transformResponse, response);\n        response.headers = AxiosHeaders$1.from(response.headers);\n        return response;\n    }, function onAdapterRejection(reason) {\n        if (!isCancel(reason)) {\n            throwIfCancellationRequested(config);\n            // Transform response data\n            if (reason && reason.response) {\n                reason.response.data = transformData.call(config, config.transformResponse, reason.response);\n                reason.response.headers = AxiosHeaders$1.from(reason.response.headers);\n            }\n        }\n        return Promise.reject(reason);\n    });\n}\nconst validators$1 = {};\n// eslint-disable-next-line func-names\n[\n    \"object\",\n    \"boolean\",\n    \"number\",\n    \"function\",\n    \"string\",\n    \"symbol\"\n].forEach((type, i)=>{\n    validators$1[type] = function validator(thing) {\n        return typeof thing === type || \"a\" + (i < 1 ? \"n \" : \" \") + type;\n    };\n});\nconst deprecatedWarnings = {};\n/**\n * Transitional option validator\n *\n * @param {function|boolean?} validator - set to false if the transitional option has been removed\n * @param {string?} version - deprecated version / removed since version\n * @param {string?} message - some message with additional info\n *\n * @returns {function}\n */ validators$1.transitional = function transitional(validator, version, message) {\n    function formatMessage(opt, desc) {\n        return \"[Axios v\" + VERSION + \"] Transitional option '\" + opt + \"'\" + desc + (message ? \". \" + message : \"\");\n    }\n    // eslint-disable-next-line func-names\n    return (value, opt, opts)=>{\n        if (validator === false) {\n            throw new AxiosError(formatMessage(opt, \" has been removed\" + (version ? \" in \" + version : \"\")), AxiosError.ERR_DEPRECATED);\n        }\n        if (version && !deprecatedWarnings[opt]) {\n            deprecatedWarnings[opt] = true;\n            // eslint-disable-next-line no-console\n            console.warn(formatMessage(opt, \" has been deprecated since v\" + version + \" and will be removed in the near future\"));\n        }\n        return validator ? validator(value, opt, opts) : true;\n    };\n};\n/**\n * Assert object's properties type\n *\n * @param {object} options\n * @param {object} schema\n * @param {boolean?} allowUnknown\n *\n * @returns {object}\n */ function assertOptions(options, schema, allowUnknown) {\n    if (typeof options !== \"object\") {\n        throw new AxiosError(\"options must be an object\", AxiosError.ERR_BAD_OPTION_VALUE);\n    }\n    const keys = Object.keys(options);\n    let i = keys.length;\n    while(i-- > 0){\n        const opt = keys[i];\n        const validator = schema[opt];\n        if (validator) {\n            const value = options[opt];\n            const result = value === undefined || validator(value, opt, options);\n            if (result !== true) {\n                throw new AxiosError(\"option \" + opt + \" must be \" + result, AxiosError.ERR_BAD_OPTION_VALUE);\n            }\n            continue;\n        }\n        if (allowUnknown !== true) {\n            throw new AxiosError(\"Unknown option \" + opt, AxiosError.ERR_BAD_OPTION);\n        }\n    }\n}\nconst validator = {\n    assertOptions,\n    validators: validators$1\n};\nconst validators = validator.validators;\n/**\n * Create a new instance of Axios\n *\n * @param {Object} instanceConfig The default config for the instance\n *\n * @return {Axios} A new instance of Axios\n */ class Axios {\n    constructor(instanceConfig){\n        this.defaults = instanceConfig;\n        this.interceptors = {\n            request: new InterceptorManager$1(),\n            response: new InterceptorManager$1()\n        };\n    }\n    /**\n   * Dispatch a request\n   *\n   * @param {String|Object} configOrUrl The config specific for this request (merged with this.defaults)\n   * @param {?Object} config\n   *\n   * @returns {Promise} The Promise to be fulfilled\n   */ async request(configOrUrl, config) {\n        try {\n            return await this._request(configOrUrl, config);\n        } catch (err) {\n            if (err instanceof Error) {\n                let dummy;\n                Error.captureStackTrace ? Error.captureStackTrace(dummy = {}) : dummy = new Error();\n                // slice off the Error: ... line\n                const stack = dummy.stack ? dummy.stack.replace(/^.+\\n/, \"\") : \"\";\n                try {\n                    if (!err.stack) {\n                        err.stack = stack;\n                    // match without the 2 top stack lines\n                    } else if (stack && !String(err.stack).endsWith(stack.replace(/^.+\\n.+\\n/, \"\"))) {\n                        err.stack += \"\\n\" + stack;\n                    }\n                } catch (e) {\n                // ignore the case where \"stack\" is an un-writable property\n                }\n            }\n            throw err;\n        }\n    }\n    _request(configOrUrl, config) {\n        /*eslint no-param-reassign:0*/ // Allow for axios('example/url'[, config]) a la fetch API\n        if (typeof configOrUrl === \"string\") {\n            config = config || {};\n            config.url = configOrUrl;\n        } else {\n            config = configOrUrl || {};\n        }\n        config = mergeConfig(this.defaults, config);\n        const { transitional, paramsSerializer, headers } = config;\n        if (transitional !== undefined) {\n            validator.assertOptions(transitional, {\n                silentJSONParsing: validators.transitional(validators.boolean),\n                forcedJSONParsing: validators.transitional(validators.boolean),\n                clarifyTimeoutError: validators.transitional(validators.boolean)\n            }, false);\n        }\n        if (paramsSerializer != null) {\n            if (utils$1.isFunction(paramsSerializer)) {\n                config.paramsSerializer = {\n                    serialize: paramsSerializer\n                };\n            } else {\n                validator.assertOptions(paramsSerializer, {\n                    encode: validators.function,\n                    serialize: validators.function\n                }, true);\n            }\n        }\n        // Set config.method\n        config.method = (config.method || this.defaults.method || \"get\").toLowerCase();\n        // Flatten headers\n        let contextHeaders = headers && utils$1.merge(headers.common, headers[config.method]);\n        headers && utils$1.forEach([\n            \"delete\",\n            \"get\",\n            \"head\",\n            \"post\",\n            \"put\",\n            \"patch\",\n            \"common\"\n        ], (method)=>{\n            delete headers[method];\n        });\n        config.headers = AxiosHeaders$1.concat(contextHeaders, headers);\n        // filter out skipped interceptors\n        const requestInterceptorChain = [];\n        let synchronousRequestInterceptors = true;\n        this.interceptors.request.forEach(function unshiftRequestInterceptors(interceptor) {\n            if (typeof interceptor.runWhen === \"function\" && interceptor.runWhen(config) === false) {\n                return;\n            }\n            synchronousRequestInterceptors = synchronousRequestInterceptors && interceptor.synchronous;\n            requestInterceptorChain.unshift(interceptor.fulfilled, interceptor.rejected);\n        });\n        const responseInterceptorChain = [];\n        this.interceptors.response.forEach(function pushResponseInterceptors(interceptor) {\n            responseInterceptorChain.push(interceptor.fulfilled, interceptor.rejected);\n        });\n        let promise;\n        let i = 0;\n        let len;\n        if (!synchronousRequestInterceptors) {\n            const chain = [\n                dispatchRequest.bind(this),\n                undefined\n            ];\n            chain.unshift.apply(chain, requestInterceptorChain);\n            chain.push.apply(chain, responseInterceptorChain);\n            len = chain.length;\n            promise = Promise.resolve(config);\n            while(i < len){\n                promise = promise.then(chain[i++], chain[i++]);\n            }\n            return promise;\n        }\n        len = requestInterceptorChain.length;\n        let newConfig = config;\n        i = 0;\n        while(i < len){\n            const onFulfilled = requestInterceptorChain[i++];\n            const onRejected = requestInterceptorChain[i++];\n            try {\n                newConfig = onFulfilled(newConfig);\n            } catch (error) {\n                onRejected.call(this, error);\n                break;\n            }\n        }\n        try {\n            promise = dispatchRequest.call(this, newConfig);\n        } catch (error) {\n            return Promise.reject(error);\n        }\n        i = 0;\n        len = responseInterceptorChain.length;\n        while(i < len){\n            promise = promise.then(responseInterceptorChain[i++], responseInterceptorChain[i++]);\n        }\n        return promise;\n    }\n    getUri(config) {\n        config = mergeConfig(this.defaults, config);\n        const fullPath = buildFullPath(config.baseURL, config.url);\n        return buildURL(fullPath, config.params, config.paramsSerializer);\n    }\n}\n// Provide aliases for supported request methods\nutils$1.forEach([\n    \"delete\",\n    \"get\",\n    \"head\",\n    \"options\"\n], function forEachMethodNoData(method) {\n    /*eslint func-names:0*/ Axios.prototype[method] = function(url, config) {\n        return this.request(mergeConfig(config || {}, {\n            method,\n            url,\n            data: (config || {}).data\n        }));\n    };\n});\nutils$1.forEach([\n    \"post\",\n    \"put\",\n    \"patch\"\n], function forEachMethodWithData(method) {\n    /*eslint func-names:0*/ function generateHTTPMethod(isForm) {\n        return function httpMethod(url, data, config) {\n            return this.request(mergeConfig(config || {}, {\n                method,\n                headers: isForm ? {\n                    \"Content-Type\": \"multipart/form-data\"\n                } : {},\n                url,\n                data\n            }));\n        };\n    }\n    Axios.prototype[method] = generateHTTPMethod();\n    Axios.prototype[method + \"Form\"] = generateHTTPMethod(true);\n});\nconst Axios$1 = Axios;\n/**\n * A `CancelToken` is an object that can be used to request cancellation of an operation.\n *\n * @param {Function} executor The executor function.\n *\n * @returns {CancelToken}\n */ class CancelToken {\n    constructor(executor){\n        if (typeof executor !== \"function\") {\n            throw new TypeError(\"executor must be a function.\");\n        }\n        let resolvePromise;\n        this.promise = new Promise(function promiseExecutor(resolve) {\n            resolvePromise = resolve;\n        });\n        const token = this;\n        // eslint-disable-next-line func-names\n        this.promise.then((cancel)=>{\n            if (!token._listeners) return;\n            let i = token._listeners.length;\n            while(i-- > 0){\n                token._listeners[i](cancel);\n            }\n            token._listeners = null;\n        });\n        // eslint-disable-next-line func-names\n        this.promise.then = (onfulfilled)=>{\n            let _resolve;\n            // eslint-disable-next-line func-names\n            const promise = new Promise((resolve)=>{\n                token.subscribe(resolve);\n                _resolve = resolve;\n            }).then(onfulfilled);\n            promise.cancel = function reject() {\n                token.unsubscribe(_resolve);\n            };\n            return promise;\n        };\n        executor(function cancel(message, config, request) {\n            if (token.reason) {\n                // Cancellation has already been requested\n                return;\n            }\n            token.reason = new CanceledError(message, config, request);\n            resolvePromise(token.reason);\n        });\n    }\n    /**\n   * Throws a `CanceledError` if cancellation has been requested.\n   */ throwIfRequested() {\n        if (this.reason) {\n            throw this.reason;\n        }\n    }\n    /**\n   * Subscribe to the cancel signal\n   */ subscribe(listener) {\n        if (this.reason) {\n            listener(this.reason);\n            return;\n        }\n        if (this._listeners) {\n            this._listeners.push(listener);\n        } else {\n            this._listeners = [\n                listener\n            ];\n        }\n    }\n    /**\n   * Unsubscribe from the cancel signal\n   */ unsubscribe(listener) {\n        if (!this._listeners) {\n            return;\n        }\n        const index = this._listeners.indexOf(listener);\n        if (index !== -1) {\n            this._listeners.splice(index, 1);\n        }\n    }\n    toAbortSignal() {\n        const controller = new AbortController();\n        const abort = (err)=>{\n            controller.abort(err);\n        };\n        this.subscribe(abort);\n        controller.signal.unsubscribe = ()=>this.unsubscribe(abort);\n        return controller.signal;\n    }\n    /**\n   * Returns an object that contains a new `CancelToken` and a function that, when called,\n   * cancels the `CancelToken`.\n   */ static source() {\n        let cancel;\n        const token = new CancelToken(function executor(c) {\n            cancel = c;\n        });\n        return {\n            token,\n            cancel\n        };\n    }\n}\nconst CancelToken$1 = CancelToken;\n/**\n * Syntactic sugar for invoking a function and expanding an array for arguments.\n *\n * Common use case would be to use `Function.prototype.apply`.\n *\n *  ```js\n *  function f(x, y, z) {}\n *  var args = [1, 2, 3];\n *  f.apply(null, args);\n *  ```\n *\n * With `spread` this example can be re-written.\n *\n *  ```js\n *  spread(function(x, y, z) {})([1, 2, 3]);\n *  ```\n *\n * @param {Function} callback\n *\n * @returns {Function}\n */ function spread(callback) {\n    return function wrap(arr) {\n        return callback.apply(null, arr);\n    };\n}\n/**\n * Determines whether the payload is an error thrown by Axios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */ function isAxiosError(payload) {\n    return utils$1.isObject(payload) && payload.isAxiosError === true;\n}\nconst HttpStatusCode = {\n    Continue: 100,\n    SwitchingProtocols: 101,\n    Processing: 102,\n    EarlyHints: 103,\n    Ok: 200,\n    Created: 201,\n    Accepted: 202,\n    NonAuthoritativeInformation: 203,\n    NoContent: 204,\n    ResetContent: 205,\n    PartialContent: 206,\n    MultiStatus: 207,\n    AlreadyReported: 208,\n    ImUsed: 226,\n    MultipleChoices: 300,\n    MovedPermanently: 301,\n    Found: 302,\n    SeeOther: 303,\n    NotModified: 304,\n    UseProxy: 305,\n    Unused: 306,\n    TemporaryRedirect: 307,\n    PermanentRedirect: 308,\n    BadRequest: 400,\n    Unauthorized: 401,\n    PaymentRequired: 402,\n    Forbidden: 403,\n    NotFound: 404,\n    MethodNotAllowed: 405,\n    NotAcceptable: 406,\n    ProxyAuthenticationRequired: 407,\n    RequestTimeout: 408,\n    Conflict: 409,\n    Gone: 410,\n    LengthRequired: 411,\n    PreconditionFailed: 412,\n    PayloadTooLarge: 413,\n    UriTooLong: 414,\n    UnsupportedMediaType: 415,\n    RangeNotSatisfiable: 416,\n    ExpectationFailed: 417,\n    ImATeapot: 418,\n    MisdirectedRequest: 421,\n    UnprocessableEntity: 422,\n    Locked: 423,\n    FailedDependency: 424,\n    TooEarly: 425,\n    UpgradeRequired: 426,\n    PreconditionRequired: 428,\n    TooManyRequests: 429,\n    RequestHeaderFieldsTooLarge: 431,\n    UnavailableForLegalReasons: 451,\n    InternalServerError: 500,\n    NotImplemented: 501,\n    BadGateway: 502,\n    ServiceUnavailable: 503,\n    GatewayTimeout: 504,\n    HttpVersionNotSupported: 505,\n    VariantAlsoNegotiates: 506,\n    InsufficientStorage: 507,\n    LoopDetected: 508,\n    NotExtended: 510,\n    NetworkAuthenticationRequired: 511\n};\nObject.entries(HttpStatusCode).forEach(([key, value])=>{\n    HttpStatusCode[value] = key;\n});\nconst HttpStatusCode$1 = HttpStatusCode;\n/**\n * Create an instance of Axios\n *\n * @param {Object} defaultConfig The default config for the instance\n *\n * @returns {Axios} A new instance of Axios\n */ function createInstance(defaultConfig) {\n    const context = new Axios$1(defaultConfig);\n    const instance = bind(Axios$1.prototype.request, context);\n    // Copy axios.prototype to instance\n    utils$1.extend(instance, Axios$1.prototype, context, {\n        allOwnKeys: true\n    });\n    // Copy context to instance\n    utils$1.extend(instance, context, null, {\n        allOwnKeys: true\n    });\n    // Factory for creating new instances\n    instance.create = function create(instanceConfig) {\n        return createInstance(mergeConfig(defaultConfig, instanceConfig));\n    };\n    return instance;\n}\n// Create the default instance to be exported\nconst axios = createInstance(defaults$1);\n// Expose Axios class to allow class inheritance\naxios.Axios = Axios$1;\n// Expose Cancel & CancelToken\naxios.CanceledError = CanceledError;\naxios.CancelToken = CancelToken$1;\naxios.isCancel = isCancel;\naxios.VERSION = VERSION;\naxios.toFormData = toFormData;\n// Expose AxiosError class\naxios.AxiosError = AxiosError;\n// alias for CanceledError for backward compatibility\naxios.Cancel = axios.CanceledError;\n// Expose all/spread\naxios.all = function all(promises) {\n    return Promise.all(promises);\n};\naxios.spread = spread;\n// Expose isAxiosError\naxios.isAxiosError = isAxiosError;\n// Expose mergeConfig\naxios.mergeConfig = mergeConfig;\naxios.AxiosHeaders = AxiosHeaders$1;\naxios.formToJSON = (thing)=>formDataToJSON(utils$1.isHTMLForm(thing) ? new FormData(thing) : thing);\naxios.getAdapter = adapters.getAdapter;\naxios.HttpStatusCode = HttpStatusCode$1;\naxios.default = axios;\nmodule.exports = axios; //# sourceMappingURL=axios.cjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/axios/dist/node/axios.cjs\n");

/***/ })

};
;