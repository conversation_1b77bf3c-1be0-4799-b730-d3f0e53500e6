import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  ListDocumentVersionsRequest,
  ListDocumentVersionsResult,
} from "../models/models_1";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface ListDocumentVersionsCommandInput
  extends ListDocumentVersionsRequest {}
export interface ListDocumentVersionsCommandOutput
  extends ListDocumentVersionsResult,
    __MetadataBearer {}
declare const ListDocumentVersionsCommand_base: {
  new (
    input: ListDocumentVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListDocumentVersionsCommandInput,
    ListDocumentVersionsCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: ListDocumentVersionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListDocumentVersionsCommandInput,
    ListDocumentVersionsCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListDocumentVersionsCommand extends ListDocumentVersionsCommand_base {
  protected static __types: {
    api: {
      input: ListDocumentVersionsRequest;
      output: ListDocumentVersionsResult;
    };
    sdk: {
      input: ListDocumentVersionsCommandInput;
      output: ListDocumentVersionsCommandOutput;
    };
  };
}
