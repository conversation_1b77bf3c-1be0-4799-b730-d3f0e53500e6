import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  DescribeInstancePatchStatesForPatchGroupRequest,
  DescribeInstancePatchStatesForPatchGroupResult,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  SSMClientResolvedConfig,
} from "../SSMClient";
export { __MetadataBearer };
export { $Command };
export interface DescribeInstancePatchStatesForPatchGroupCommandInput
  extends DescribeInstancePatchStatesForPatchGroupRequest {}
export interface DescribeInstancePatchStatesForPatchGroupCommandOutput
  extends DescribeInstancePatchStatesForPatchGroupResult,
    __MetadataBearer {}
declare const DescribeInstancePatchStatesForPatchGroupCommand_base: {
  new (
    input: DescribeInstancePatchStatesForPatchGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInstancePatchStatesForPatchGroupCommandInput,
    DescribeInstancePatchStatesForPatchGroupCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: DescribeInstancePatchStatesForPatchGroupCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    DescribeInstancePatchStatesForPatchGroupCommandInput,
    DescribeInstancePatchStatesForPatchGroupCommandOutput,
    SSMClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class DescribeInstancePatchStatesForPatchGroupCommand extends DescribeInstancePatchStatesForPatchGroupCommand_base {
  protected static __types: {
    api: {
      input: DescribeInstancePatchStatesForPatchGroupRequest;
      output: DescribeInstancePatchStatesForPatchGroupResult;
    };
    sdk: {
      input: DescribeInstancePatchStatesForPatchGroupCommandInput;
      output: DescribeInstancePatchStatesForPatchGroupCommandOutput;
    };
  };
}
